
dac5724137aa0b521c08748cf58c1ae0de056a8f	{"key":"{\"nodeVersion\":\"v16.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"4b3ffa2eefd7093d49a8531c56cf382d\"}","integrity":"sha512-x1rr6h05umGhxkTAE3iDxqEm46iOPkXAAkA6rbatILnmJA1yVCl8Au9CsrWZMImTW2Zv71a9MpS6hQyFLsxzrw==","time":1753843769571,"size":3428430}