{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\定阔\\dingkuao\\定扩web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\定阔\\dingkuao\\定扩web\\src\\views\\mThree\\curls\\index.vue?vue&type=style&index=0&id=6c0ad646&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\定阔\\dingkuao\\定扩web\\src\\views\\mThree\\curls\\index.vue", "mtime": 1753843713643}, {"path": "C:\\Users\\<USER>\\Desktop\\定阔\\dingkuao\\定扩web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1713250439929}, {"path": "C:\\Users\\<USER>\\Desktop\\定阔\\dingkuao\\定扩web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1713250441304}, {"path": "C:\\Users\\<USER>\\Desktop\\定阔\\dingkuao\\定扩web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1713250440419}, {"path": "C:\\Users\\<USER>\\Desktop\\定阔\\dingkuao\\定扩web\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1713250439488}, {"path": "C:\\Users\\<USER>\\Desktop\\定阔\\dingkuao\\定扩web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1713250439495}, {"path": "C:\\Users\\<USER>\\Desktop\\定阔\\dingkuao\\定扩web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1713250440827}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmFwcC1jb250YWluZXIgew0KICBwYWRkaW5nOiAyMHB4Ow0KfQ0KLm1iNSB7DQogIG1hcmdpbi1ib3R0b206IDVweDsNCn0NCi5tYjggew0KICBtYXJnaW4tdG9wOiAyMHB4Ow0KICBtYXJnaW4tYm90dG9tOiA4cHg7DQp9DQouZWwtZm9ybS1zZWFyY2gtaXRlbSB7DQogIG1hcmdpbi1ib3R0b206IDA7DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+jBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/mThree/curls", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"10\" class=\"mb5\">\r\n      <!-- 右对齐搜索区域 -->\r\n      <el-col>\r\n        <el-form\r\n          :model=\"queryParams\"\r\n          ref=\"queryForm\"\r\n          :inline=\"true\"\r\n          label-width=\"80px\"\r\n        >\r\n          <el-form-item\r\n            label=\"部门\"\r\n            prop=\"deptName\"\r\n            class=\"el-form-search-item\"\r\n          >\r\n            <el-input\r\n              v-model=\"queryParams.deptName\"\r\n              placeholder=\"请输入部门名称\"\r\n              clearable\r\n              size=\"small\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item\r\n            label=\"业务员\"\r\n            prop=\"applyName\"\r\n            class=\"el-form-search-item\"\r\n          >\r\n            <el-input\r\n              v-model=\"queryParams.applyName\"\r\n              placeholder=\"请输入员工姓名\"\r\n              clearable\r\n              size=\"small\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item\r\n            label=\"发放状态\"\r\n            prop=\"redCard\"\r\n            class=\"el-form-search-item\"\r\n          >\r\n            <el-select\r\n              v-model=\"queryParams.redCard\"\r\n              clearable\r\n              placeholder=\"请选择发放状态\"\r\n            >\r\n              <el-option label=\"待发放\" :value=\"0\" />\r\n              <el-option label=\"已发放\" :value=\"1\" />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item\r\n            label=\"时间范围\"\r\n            prop=\"dateRange\"\r\n            class=\"el-form-search-item\"\r\n          >\r\n            <el-date-picker\r\n              v-model=\"dateRange\"\r\n              type=\"daterange\"\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n              value-format=\"yyyy-MM-dd\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item class=\"el-form-search-item\">\r\n            <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-search\"\r\n              size=\"mini\"\r\n              @click=\"handleQuery\"\r\n              >搜索</el-button\r\n            >\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n              >重置</el-button\r\n            >\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 操作按钮区域 -->\r\n    <el-row class=\"mb8\" :gutter=\"10\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          size=\"mini\"\r\n          @click=\"handleResend\"\r\n          :disabled=\"!selectedRows.length\"\r\n        >\r\n          一键补发卡卷\r\n        </el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExportDialog\"\r\n        >\r\n          导出数据\r\n        </el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"info\"\r\n          plain\r\n          icon=\"el-icon-document-copy\"\r\n          size=\"mini\"\r\n          @click=\"handleExportWriteOffDetail\"\r\n        >\r\n          导出核销明细\r\n        </el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-s-promotion\"\r\n          size=\"mini\"\r\n          @click=\"handleSendCardListDialog\"\r\n        >\r\n          发送一元卡券\r\n        </el-button>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"tableData\"\r\n      border\r\n      style=\"width: 100%\"\r\n      :height=\"tableHeight\"\r\n      @selection-change=\"handleSelectionChange\"\r\n      ref=\"table\"\r\n    >\r\n      <el-table-column\r\n        type=\"selection\"\r\n        width=\"55\"\r\n        align=\"center\"\r\n        :selectable=\"checkSelectable\"\r\n      />\r\n      <el-table-column label=\"#\" type=\"index\" width=\"50\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{\r\n            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1\r\n          }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"nickName\" label=\"客户姓名\" align=\"center\" />\r\n      <el-table-column\r\n        prop=\"phonenumber\"\r\n        label=\"客户手机号\"\r\n        align=\"center\"\r\n        width=\"120\"\r\n      />\r\n      <!-- <el-table-column prop=\"orderId\" label=\"订单ID\" align=\"center\" /> -->\r\n\r\n      <el-table-column prop=\"applyName\" label=\"业务员\" align=\"center\" />\r\n      <el-table-column prop=\"deptName\" label=\"所属部门\" align=\"center\" />\r\n      <el-table-column prop=\"redCard\" label=\"发放状态\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag :type=\"scope.row.redCard ? 'success' : 'primary'\">\r\n            {{ scope.row.redCard ? \"已发放\" : \"待发放\" }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"orderAmount\" label=\"红包金额\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          {{\r\n            scope.row.orderAmount ? scope.row.orderAmount + \"元\" : \"谢谢惠顾\"\r\n          }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        prop=\"createTime\"\r\n        label=\"发放时间\"\r\n        align=\"center\"\r\n        width=\"160\"\r\n      />\r\n      <el-table-column label=\"操作\" align=\"center\" width=\"200\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            v-if=\"!scope.row.redCard\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-s-promotion\"\r\n            @click=\"handleEdit(scope.row)\"\r\n            >补发卡卷</el-button\r\n          >\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      :page-sizes=\"[20, 30, 50, 100]\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加导出选项弹框 -->\r\n    <el-dialog\r\n      title=\"导出选项\"\r\n      :visible.sync=\"exportDialog\"\r\n      width=\"500px\"\r\n      append-to-body\r\n    >\r\n      <el-form\r\n        ref=\"exportForm\"\r\n        :model=\"exportForm\"\r\n        :rules=\"exportRules\"\r\n        label-width=\"80px\"\r\n      >\r\n        <el-form-item label=\"时间范围\" prop=\"dateRange\">\r\n          <el-date-picker\r\n            v-model=\"exportForm.dateRange\"\r\n            type=\"daterange\"\r\n            range-separator=\"至\"\r\n            start-placeholder=\"开始日期\"\r\n            end-placeholder=\"结束日期\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            style=\"width: 100%\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"导出类型\" prop=\"exportType\">\r\n          <el-select\r\n            v-model=\"exportForm.exportType\"\r\n            placeholder=\"请选择导出类型\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option label=\"客户领取明细\" value=\"customer\" />\r\n            <el-option label=\"员工发卷明细\" value=\"staff\" />\r\n            <el-option label=\"项目组发卷明细\" value=\"dept\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"exportDialog = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmExport\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 发送卡券列表弹框 -->\r\n    <el-dialog\r\n      title=\"发送卡券列表\"\r\n      :visible.sync=\"sendCardListDialog\"\r\n      width=\"400px\"\r\n      append-to-body\r\n    >\r\n      <el-form\r\n        ref=\"sendCardListForm\"\r\n        :model=\"sendCardListForm\"\r\n        :rules=\"sendCardListRules\"\r\n        label-width=\"80px\"\r\n      >\r\n        <el-form-item label=\"批次码\" prop=\"stockId\">\r\n          <el-input\r\n            v-model=\"sendCardListForm.stockId\"\r\n            placeholder=\"请输入批次码\"\r\n            clearable\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"批次选项\" prop=\"type\">\r\n          <el-select\r\n            v-model=\"sendCardListForm.type\"\r\n            placeholder=\"请选择批次\"\r\n            clearable\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option label=\"批次1\" :value=\"1\" />\r\n            <el-option label=\"批次2\" :value=\"2\" />\r\n            <el-option label=\"批次3\" :value=\"3\" />\r\n            <el-option label=\"批次4\" :value=\"4\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"sendCardListDialog = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmSendCardList\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from \"axios\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport { getCurlsList, sendCard, sendCardList } from \"@/api/system/curls\";\r\n\r\nexport default {\r\n  name: \"Curls\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 总条数\r\n      total: 0,\r\n      // 表格数据\r\n      tableData: [],\r\n      // 查询参数\r\n      dateRange: [],\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        deptName: undefined,\r\n        applyName: undefined,\r\n        redCard: undefined,\r\n        startTime: undefined,\r\n        endTime: undefined,\r\n      },\r\n      // 导出弹框显示状态\r\n      exportDialog: false,\r\n      // 导出表单参数\r\n      exportForm: {\r\n        dateRange: [],\r\n        exportType: undefined,\r\n      },\r\n      // 导出表单校验规则\r\n      exportRules: {\r\n        dateRange: [\r\n          { required: true, message: \"请选择时间范围\", trigger: \"change\" },\r\n        ],\r\n        exportType: [\r\n          { required: true, message: \"请选择导出类型\", trigger: \"change\" },\r\n        ],\r\n      },\r\n      tableHeight: 650,\r\n      selectedRows: [], // 新增选中行数组\r\n      // 发送卡券列表弹框显示状态\r\n      sendCardListDialog: false,\r\n      // 发送卡券列表表单参数\r\n      sendCardListForm: {\r\n        stockId: \"\",\r\n      },\r\n      // 发送卡券列表表单校验规则\r\n      sendCardListRules: {\r\n        stockId: [{ required: true, message: \"请输入批次码\", trigger: \"blur\" }],\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询列表 */\r\n    async getList() {\r\n      this.loading = true;\r\n\r\n      // 处理时间参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        this.queryParams.startTime = this.dateRange[0];\r\n        this.queryParams.endTime = this.dateRange[1];\r\n      }\r\n\r\n      try {\r\n        const res = await getCurlsList(this.queryParams);\r\n        this.tableData = res.rows;\r\n        this.total = res.total;\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.dateRange = [];\r\n      this.selectedRows = [];\r\n      if (this.$refs.table) {\r\n        this.$refs.table.clearSelection();\r\n      }\r\n      this.handleQuery();\r\n    },\r\n    /** 补发按钮操作 */\r\n    handleEdit(row) {\r\n      this.$modal\r\n        .confirm(`确认要对\"${row.nickName}\"进行补发操作吗？`)\r\n        .then(async () => {\r\n          try {\r\n            this.$modal.loading(\"正在补发中，请稍候...\");\r\n            // 调用补发接口，单个补发时传入单个id的数组\r\n            await sendCard({ list: [row.openid] });\r\n            this.$modal.msgSuccess(\"补发成功\");\r\n            this.getList();\r\n          } catch (error) {\r\n            console.error(\"补发失败:\", error);\r\n            this.$modal.msgError(\"补发失败，请重试\");\r\n          } finally {\r\n            this.$modal.closeLoading();\r\n          }\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /** 表格多选框选中数据 */\r\n    handleSelectionChange(selection) {\r\n      this.selectedRows = selection.filter((row) => !row.redCard);\r\n    },\r\n    /** 一键补发按钮操作 */\r\n    handleResend() {\r\n      if (!this.selectedRows.length) {\r\n        this.$modal.msgError(\"请先选择要补发的客户\");\r\n        return;\r\n      }\r\n\r\n      const names = this.selectedRows.map((item) => item.nickName).join(\"、\");\r\n      this.$modal\r\n        .confirm(`确认要对\"${names}\"进行补发操作吗？`)\r\n        .then(async () => {\r\n          try {\r\n            this.$modal.loading(\"正在补发中，请稍候...\");\r\n            // 调用补发接口，传入选中行的openid数组\r\n            const openids = this.selectedRows.map((item) => item.openid);\r\n            await sendCard({ list: openids });\r\n            this.$modal.msgSuccess(\"补发成功\");\r\n            this.getList();\r\n          } catch (error) {\r\n            console.error(\"补发失败:\", error);\r\n            this.$modal.msgError(\"补发失败，请重试\");\r\n          } finally {\r\n            this.$modal.closeLoading();\r\n          }\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /** 显示导出弹框 */\r\n    handleExportDialog() {\r\n      this.exportDialog = true;\r\n      this.exportForm = {\r\n        dateRange: [],\r\n        exportType: undefined,\r\n      };\r\n    },\r\n    /** 确认导出操作 */\r\n    confirmExport() {\r\n      this.$refs.exportForm.validate((valid) => {\r\n        if (valid) {\r\n          const params = {\r\n            startTime: this.exportForm.dateRange[0],\r\n            endTime: this.exportForm.dateRange[1],\r\n          };\r\n\r\n          this.$modal.confirm(\"是否确认导出数据？\").then(() => {\r\n            this.$modal.loading(\"正在导出数据，请稍候\");\r\n\r\n            // 根据选择的导出类型确定接口地址\r\n            const urlMap = {\r\n              customer: \"/system/curls/export\",\r\n              staff: \"/system/curls/exportStaff\",\r\n              dept: \"/system/curls/exportDept\",\r\n            };\r\n\r\n            // 根据选择的导出类型确定文件名\r\n            const fileNameMap = {\r\n              customer: `${params.startTime}-${params.endTime}客户领取明细`,\r\n              staff: `${params.startTime}-${params.endTime}员工发卷明细`,\r\n              dept: `${params.startTime}-${params.endTime}项目组发卷明细`,\r\n            };\r\n\r\n            axios({\r\n              method: \"post\",\r\n              headers: {\r\n                Authorization: \"Bearer \" + getToken(),\r\n              },\r\n              url:\r\n                process.env.VUE_APP_BASE_API +\r\n                urlMap[this.exportForm.exportType],\r\n              data: params,\r\n              responseType: \"blob\",\r\n            })\r\n              .then((res) => {\r\n                this.$modal.closeLoading();\r\n                const link = document.createElement(\"a\");\r\n                let blob = new Blob([res.data], {\r\n                  type: \"application/vnd.ms-excel\",\r\n                });\r\n                link.style.display = \"none\";\r\n                link.href = URL.createObjectURL(blob);\r\n                link.download =\r\n                  fileNameMap[this.exportForm.exportType] + \".xlsx\";\r\n                document.body.appendChild(link);\r\n                link.click();\r\n                document.body.removeChild(link);\r\n                this.exportDialog = false;\r\n              })\r\n              .catch(() => {\r\n                this.$modal.closeLoading();\r\n              });\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 导出核销明细 */\r\n    handleExportWriteOffDetail() {\r\n      this.$modal.loading(\"正在导出核销明细，请稍候\");\r\n\r\n      axios({\r\n        method: \"post\",\r\n        headers: {\r\n          Authorization: \"Bearer \" + getToken(),\r\n        },\r\n        url: process.env.VUE_APP_BASE_API + \"/business/workData/exportCard\",\r\n        responseType: \"blob\",\r\n      })\r\n        .then((res) => {\r\n          this.$modal.closeLoading();\r\n          const link = document.createElement(\"a\");\r\n          let blob = new Blob([res.data], {\r\n            type: \"application/vnd.ms-excel\",\r\n          });\r\n          link.style.display = \"none\";\r\n          link.href = URL.createObjectURL(blob);\r\n          link.download = \"核销明细.xlsx\";\r\n          document.body.appendChild(link);\r\n          link.click();\r\n          document.body.removeChild(link);\r\n          this.$modal.msgSuccess(\"导出成功\");\r\n        })\r\n        .catch(() => {\r\n          this.$modal.closeLoading();\r\n          this.$modal.msgError(\"导出失败，请重试\");\r\n        });\r\n    },\r\n    /** 判断行是否可选 */\r\n    checkSelectable(row) {\r\n      return !row.redCard; // redCard为1时返回false，表示不可选\r\n    },\r\n    /** 显示发送卡券列表弹框 */\r\n    handleSendCardListDialog() {\r\n      this.sendCardListDialog = true;\r\n      this.sendCardListForm = {\r\n        stockId: \"\",\r\n      };\r\n    },\r\n    /** 确认发送卡券列表操作 */\r\n    confirmSendCardList() {\r\n      this.$refs.sendCardListForm.validate(async (valid) => {\r\n        if (valid) {\r\n          try {\r\n            this.$modal.loading(\"正在发送卡券列表，请稍候...\");\r\n            await sendCardList(this.sendCardListForm.stockId);\r\n            this.$modal.msgSuccess(\"发送卡券列表成功\");\r\n            this.sendCardListDialog = false;\r\n          } catch (error) {\r\n            console.error(\"发送卡券列表失败:\", error);\r\n            this.$modal.msgError(\"发送卡券列表失败，请重试\");\r\n          } finally {\r\n            this.$modal.closeLoading();\r\n          }\r\n        }\r\n      });\r\n    },\r\n  },\r\n  watch: {\r\n    // 监听数据变化，清空选中状态\r\n    tableData() {\r\n      this.$nextTick(() => {\r\n        if (this.$refs.table) {\r\n          this.$refs.table.clearSelection();\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n.mb5 {\r\n  margin-bottom: 5px;\r\n}\r\n.mb8 {\r\n  margin-top: 20px;\r\n  margin-bottom: 8px;\r\n}\r\n.el-form-search-item {\r\n  margin-bottom: 0;\r\n}\r\n</style>\r\n"]}]}