
25ca707238e3a10b47c4963cf5e7aceabfc8baca	{"key":"{\"nodeVersion\":\"v16.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"572e01a647758f1d5c35cec2547baffd\"}","integrity":"sha512-W2DLWHqyFqZ8lNBANBCg1Qk1AXspBx89muw/hGkH3KFUPj+f6t0KMNU73dEmbv68i6L4Eki+a15DlYiECIUV8A==","time":1753843716483,"size":3428319}