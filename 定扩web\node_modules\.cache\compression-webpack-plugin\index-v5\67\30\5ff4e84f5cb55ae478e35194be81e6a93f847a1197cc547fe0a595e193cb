
2317aa83aca37d15f6fa5223b4a426b42a3a1093	{"key":"{\"nodeVersion\":\"v16.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"8e037399381fd38a4c73721e80590f7f\"}","integrity":"sha512-OXJ84jtsJn28P0kFruvsxNiBOMyEMS0QBm+W9fZ8RtkAMXBIckEPY5EpRV8C9vrWbI3uvRtvObGm7ROa6IEWuw==","time":1753843954713,"size":11431993}