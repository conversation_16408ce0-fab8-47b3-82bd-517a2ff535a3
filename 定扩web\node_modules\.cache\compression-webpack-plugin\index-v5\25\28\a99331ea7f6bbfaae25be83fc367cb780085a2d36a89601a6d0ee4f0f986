
b82fcf902b7d75f37aa77a0978a5f6b2443e4a8c	{"key":"{\"nodeVersion\":\"v16.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"ab7332a41bd3ccba263c4f33b7f2955f\"}","integrity":"sha512-8AJApCudq9ymQx4tglyUXHAvjV3MgQsSfD/QEgCEK/jQMKc3hZgsv0UxxhNenttXslXFsiCCWQNMSvThN/M9cg==","time":1753843769984,"size":11432084}