import request from "@/utils/request";

// 查询账户结算列表
export function getAccountSettleList(query) {
  return request({
    url: "/business/settle/list",
    method: "get",
    params: query,
  });
}

// 导出账户结算模板
export function exportAccountSettleModel() {
  return request({
    url: "/business/settle/exportModel",
    method: "post",
    responseType: "blob",
  });
}

// 导入账户结算数据
export function importAccountSettleData(data) {
  return request({
    url: "/business/settle/upload",
    method: "post",
    data: data,
  });
}

// 结算
export function settle(data) {
  return request({
    url: "/business/settle",
    method: "post",
    data: data,
  });
}
