
aa227c232b286bf78ecad855e3c8182c7dcd1987	{"key":"{\"nodeVersion\":\"v16.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"0.fc1490766ba865168973.hot-update.js\",\"contentHash\":\"e3f407022ff92b54384c093371bef204\"}","integrity":"sha512-azoWkxtvpDsqRFClEhSM7xGz/VdB8W2KnOU/TC9wEykAx6HG0kNzeT7GBIT/7BaFWREBVgHgav1Krx103eupJg==","time":1753843831195,"size":44931}