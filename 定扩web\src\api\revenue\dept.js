import request from "@/utils/request";

// 查询部门列表
export function getDeptList(query) {
  return request({
    url: "/business/accountDept/list",
    method: "get",
    params: query,
  });
}

// 查询部门详细
export function getDept(id) {
  return request({
    url: `/business/accountDept/${id}`,
    method: "get",
  });
}

// 新增部门
export function addDept(data) {
  return request({
    url: "/business/accountDept",
    method: "post",
    data: data,
  });
}

// 修改部门
export function updateDept(data) {
  return request({
    url: "/business/accountDept",
    method: "put",
    data: data,
  });
}

// 删除部门
export function delDept(id) {
  return request({
    url: `/business/accountDept/${id}`,
    method: "delete",
  });
}

// 导出部门数据
export function exportDept(data) {
  return request({
    url: "/business/accountDept/export",
    method: "post",
    data: data,
    responseType: "blob",
  });
}

// 导入部门数据
export function importDept(file) {
  const formData = new FormData();
  formData.append("file", file);
  return request({
    url: "/business/accountDept/upload",
    method: "post",
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}
