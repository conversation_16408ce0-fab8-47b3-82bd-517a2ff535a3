
b3abf4203748af1e17d715f57da39e7c38567f58	{"key":"{\"nodeVersion\":\"v16.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"82c35aa19d30d2587c6dccd18d0521eb\"}","integrity":"sha512-UZNvPA1l1H1oBlY/9JgzNh1hcOsuJYNpGpFE/NL4eFWhGnskoMKMqwtsHneU7n9X+bRPuHGAcvoWjq80lGLyIg==","time":1753843954269,"size":3428418}