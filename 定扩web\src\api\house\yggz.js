import request from "@/utils/request";

// 查询列表
export function listHouseVillage(query) {
  return request({
    url: "/system/exstaff/list",
    method: "get",
    params: query,
  });
}

// 查询一级表头列表
export function listHeaders() {
  return request({
    url: "/system/dict/data/type/up_table_title",
    method: "get",
  });
}

// 查询房源小区详细
export function getHouseVillage(id) {
  return request({
    url: "/system/exstaff/" + id,
    method: "get",
  });
}

// 新增表头列表
export function addHouseVillage(data) {
  return request({
    url: "/system/exstaff",
    method: "post",
    data: data,
  });
}

// 修改表头
export function updateHouseVillage(data) {
  return request({
    url: "/system/exstaff",
    method: "put",
    data: data,
  });
}

// 删除房源小区
export function delHouseVillage(id) {
  return request({
    url: "/system/exstaff/" + id,
    method: "delete",
  });
}
