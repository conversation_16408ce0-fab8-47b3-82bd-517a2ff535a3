{"name": "@types/q", "version": "1.5.8", "description": "TypeScript definitions for q", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/q", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "bnemetchek", "url": "https://github.com/bnemetchek"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/AndrewGaspar"}, {"name": "<PERSON>", "githubUsername": "johnny<PERSON><PERSON><PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/mboudreau"}, {"name": "TeamworkGuy2", "githubUsername": "TeamworkGuy2", "url": "https://github.com/TeamworkGuy2"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/q"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "694c6130399a0cee6c91943efb26a50a45289453a64cfc677c112e3857e30c33", "typeScriptVersion": "4.5"}