<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb5">
      <el-col :span="1.5">
        <!-- <el-button
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:houseRoom:add']"
          >新增</el-button
        > -->
      </el-col>
      <el-col :span="1.5">
        <!-- <el-button
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:houseRoom:edit']"
          >修改</el-button
        > -->
      </el-col>
      <el-col :span="1.5">
        <!-- <el-button
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:houseRoom:remove']"
          >删除</el-button
        > -->
      </el-col>
      <!-- 左对齐 -->

      <!-- 右对齐 -->
      <el-col>
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          label-width="60px"
        >
          <el-form-item
            label="员工"
            prop="nickName"
            class="el-form-search-item"
          >
            <el-input
              v-model="queryParams.nickName"
              placeholder="请输入员工姓名"
              clearable
              size="small"
            />
          </el-form-item>
          <el-form-item
            label="项目组"
            prop="deptId"
            class="el-form-search-item"
          >
            <el-select v-model="queryParams.deptId" placeholder="请选择项目组">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="时间" prop="month" class="el-form-search-item">
            <el-date-picker
              :clearable="false"
              v-model="queryParams.month"
              format="yyyy 年 MM 月"
              value-format="yyyy-MM"
              type="month"
              placeholder="请选择月份"
            >
            </el-date-picker>
          </el-form-item>

          <el-form-item class="el-form-search-item">
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </el-col>
      <div class="exl"></div>
      <el-button plain size="mini" type="success" @click="handleImportClick"
        >导入</el-button
      >
    </el-row>

    <el-table
      :height="tableHeight"
      v-loading="loading"
      :data="houseRoomList"
      ref="dataTable"
    >
      <el-table-column label="#" type="index" width="50" align="center">
        <template scope="scope">
          <span>{{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}</span>
        </template>
      </el-table-column>

      <el-table-column
        v-for="(item, index) in titleList"
        :key="item.value"
        :label="item.label"
        align="center"
        :prop="item.value"
      >
        <template slot-scope="{ row }">
          {{
            item.label === "卡状态"
              ? row[item.value] === 1
                ? "审卡"
                : row[item.value] === 2
                ? "核卡"
                : row[item.value] === 3
                ? "激活"
                : row[item.value] === 4
                ? "有效"
                : "未知"
              : item.label === "动卡"
              ? row[item.value] === 1
                ? "是"
                : "否"
              : item.label === "自动"
              ? row[item.value] === 1
                ? "是"
                : "否"
              : row[item.value]
          }}
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getVillageList"
    />

    <!-- 修改导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px">
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>

        <div class="el-upload__tip" style="color: red" slot="tip">
          提示：仅允许导入"xls"或"xlsx"格式文件！
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getListApi, deptList } from "@/api/revenue/customer";
// 导入接口
import { importWorkMonth } from "@/api/wishing";

import { titleList } from "./titleList.js";

import { getToken } from "@/utils/auth";

import axios from "axios";
export default {
  name: "M3",
  dicts: ["sys_notice_status"],
  // components: { evaluate, selectUser },
  data() {
    return {
      titleList,
      baseURL: process.env.VUE_APP_BASE_API,
      // 修改导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "月度数据导入",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/business/workMonth/upload",
      },
      // 表格高度
      tableHeight: document.documentElement.clientHeight - 280,
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 房源详情表格数据
      houseRoomList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        deptId: "",
        nickName: "",
        month: "",
      },

      // 表头列表
      headerList: [],
      // 项目组列表
      options: [],
      // 发布弹出层
      dialogVisible: false,
      // 发布项目组数组
      deptIdList: [],
      uploadLoading: false,
    };
  },
  created() {
    this.getDeptList();
    // this.getHeaders()

    this.getVillageList();
  },
  methods: {
    getDeptList() {
      deptList().then((response) => {
        this.options = response.data;
      });
    },

    /** 查询表格列表列表 */
    getVillageList() {
      this.loading = true;
      getListApi(this.queryParams).then((response) => {
        this.total = response.total;
        this.houseRoomList = response.rows.map((item) => {
          return {
            ...item,
            // 保留两位小数
            dkRatio: (item.dkRatio * 100).toFixed(2) + "%",
            kjRatio: (item.kjRatio * 100).toFixed(2) + "%",
            wxRatio: (item.wxRatio * 100).toFixed(2) + "%",
          };
        });

        this.loading = false;
      });
    },
    /** 查询项目组列表 */
    // getDeptList() {
    //   this.loading = true;
    //   deptList().then((response) => {

    //     this.options = response.data.map(item => {
    //       return { label: item.deptName, value: item.deptId };
    //     })

    //   });
    // },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getVillageList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 点击导入按钮，打开导入对话框 */
    handleImportClick() {
      this.upload.open = true;
      this.upload.title = "月度数据导入";
    },

    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },

    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg || "导入成功", "导入结果", {
        dangerouslyUseHTMLString: true,
      });
      this.getVillageList(); // 修改为正确的方法名
    },

    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },

    /** 导出按钮操作 */

    exportMethod(data) {
      axios({
        method: "post",
        headers: {
          Authorization: "Bearer " + getToken(),
        },
        url: this.baseURL + "/system/workuser/export",
        data: data,
        responseType: "blob",
      })
        .then((res) => {
          const link = document.createElement("a");
          let blob = new Blob([res.data], { type: "application/x-excel" });
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          link.download = "M3客户.xlsx";

          document.querySelector(".exl").appendChild(link);
          link.click();
        })
        .catch((error) => {
          this.$Notice.error({
            title: "错误",
            desc: "网络连接错误",
          });
        });
    },

    // 导入操作触发
    handleImportCommand(command) {
      switch (command) {
        case "handleImport":
          this.handleImport();
          break;
      }
    },

    // 重新绘制表格，防止抖动与滚动条异常
    setDataTable() {
      this.$nextTick(() => {
        this.$refs.dataTable.doLayout();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.drawer_content {
  margin: 0 30px;

  .drawer_footer {
    float: right;
    padding-bottom: 40rpx;
  }
}

.import-upload {
  display: inline-block;
  margin-left: 5px;
}
</style>
