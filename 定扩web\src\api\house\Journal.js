import request from "@/utils/request";

// 查询列表
export function listHouseVillage(query) {
  return request({
    url: "/system/expend/list",
    method: "get",
    params: query,
  });
}

// 查询一级表头列表
export function listHeaders() {
  return request({
    url: "/system/dict/data/type/up_table_title",
    method: "get",
  });
}

// 查询房源小区详细
export function getHouseVillage(id) {
  return request({
    url: "/system/expend/" + id,
    method: "get",
  });
}

// 新增表头列表
export function addHouseVillage(data) {
  return request({
    url: "/system/expend",
    method: "post",
    data: data,
  });
}

// 修改表头
export function updateHouseVillage(data) {
  return request({
    url: "/system/expend",
    method: "put",
    data: data,
  });
}

// 删除房源小区
export function delHouseVillage(id) {
  return request({
    url: "/system/expend/" + id,
    method: "delete",
  });
}

// 摘要列表
export function summarylist() {
  return request({
    url: "/system/dict/data/getDict/zhai_yao",
    method: "get",
  });
}

// 摘要列表
export function sumlist(params) {
  return request({
    url: "/system/expend/sum",
    method: "get",
    params,
  });
}
