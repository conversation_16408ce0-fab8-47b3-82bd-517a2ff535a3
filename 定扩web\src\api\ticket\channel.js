import request from "@/utils/request";

// 查询渠道列表
export function getChannelList(query) {
  return request({
    url: "/system/channel/list",
    method: "get",
    params: query,
  });
}

// 新增渠道
export function addChannel(data) {
  return request({
    url: "/system/channel",
    method: "post",
    data: data,
  });
}

// 修改渠道
export function updateChannel(data) {
  return request({
    url: "/system/channel",
    method: "put",
    data: data,
  });
}

// 删除渠道
export function deleteChannel(id) {
  return request({
    url: `/system/channel/${id}`,
    method: "delete",
  });
}
