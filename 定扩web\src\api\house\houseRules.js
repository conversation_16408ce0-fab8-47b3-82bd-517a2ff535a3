import request from "@/utils/request";

// 查询表头列表
export function MeterHeadList(type) {
  return request({
    url: "/system/table/all?type=" + type,
    method: "get",
  });
}

// 查询计算器面板列表
export function CalculatorPanelList() {
  return request({
    url: "/system/table/symbol",
    method: "get",
  });
}

// 查询项目组列表
export function deptList() {
  return request({
    url: "/system/userDept/all",
    method: "get",
  });
}

// 查询表头列表
export function listHouseVillage(query) {
  return request({
    url: "/system/exConfig/list",
    method: "get",
    params: query,
  });
}

// 查询表头规则详细
export function getHouseVillage(id) {
  return request({
    url: "/system/table/calcInfo/" + id,
    method: "get",
  });
}

// 新增表头列表
export function addHouseVillage(data) {
  return request({
    url: "/system/exConfig",
    method: "post",
    data: data,
  });
}

// 修改表头
export function updateHouseVillage(data) {
  return request({
    url: "/system/exConfig",
    method: "put",
    data: data,
  });
}
// 批量修改表头
export function updateHouseVillages(data) {
  return request({
    url: "/system/exConfig/updateList",
    method: "post",
    data: data,
  });
}

// 删除房源小区
export function delHouseVillage(id) {
  return request({
    url: "/system/exConfig/delete/" + id,
    method: "get",
  });
}

// 删除房源小区
export function getList(id) {
  return request({
    url: "/system/table/calc/" + id,
    method: "get",
  });
}
// 删除房源小区
export function tablecalcAll() {
  return request({
    url: "/system/table/calcAll",
    method: "get",
  });
}

// 项目组列表
export function deptlistapi() {
  return request({
    url: "/system/exportDept/list",
    method: "get",
  });
}

// 新增项目组
export function addDeptapi(data) {
  return request({
    url: "/system/exportDept",
    method: "post",
    data,
  });
}
// 删除项目组
export function deleteDeptapi(deptName) {
  return request({
    url: "/system/exportDept/" + deptName,
    method: "delete",
  });
}
