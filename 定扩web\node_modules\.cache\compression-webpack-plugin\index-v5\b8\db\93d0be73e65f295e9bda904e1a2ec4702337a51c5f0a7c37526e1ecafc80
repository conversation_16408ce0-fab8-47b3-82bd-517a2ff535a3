
81b56ac9d8d3259b886d9fada89af6003b17d15b	{"key":"{\"nodeVersion\":\"v16.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"5663df32fd1a58e6fa79b59599ad72b8\"}","integrity":"sha512-CCKl5S/Wr5ovFXYAz9k5Z7yp6QSQtflOYgvuk6lXeRJRm/QCBDmvx9ZqGDpmLzg7u0hb9r7smLP0Slo85WrV3g==","time":1753843745355,"size":3429022}