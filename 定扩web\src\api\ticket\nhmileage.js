import request from "@/utils/request";

// 查询订单列表
export function getOrderList(query) {
  return request({
    url: "/system/info/list",
    method: "get",
    params: query,
  });
}

// 新增订单
export function addOrder(data) {
  return request({
    url: "/system/info",
    method: "post",
    data: data,
  });
}

// 修改订单
export function updateOrder(data) {
  return request({
    url: "/system/info",
    method: "put",
    data: data,
  });
}

// 删除订单
export function deleteOrder(id) {
  return request({
    url: `/system/info/${id}`,
    method: "delete",
  });
}

// 更新回款状态
export function updatePaymentStatus(ids) {
  return request({
    url: "/system/info/returnMoney",
    method: "post",
    data: { ids },
  });
}

// 导出订单
export function exportOrder(query) {
  return request({
    url: "/system/info/export",
    method: "get",
    params: query,
    responseType: "blob",
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
  });
}
// 渠道列表

export function channellist() {
  return request({
    url: "system/channel/listAll",
    method: "get",
  });
}
