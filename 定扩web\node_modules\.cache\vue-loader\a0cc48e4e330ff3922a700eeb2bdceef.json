{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\定阔\\dingkuao\\定扩web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\定阔\\dingkuao\\定扩web\\src\\views\\house\\salary\\index.vue?vue&type=style&index=0&id=70a7a714&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\定阔\\dingkuao\\定扩web\\src\\views\\house\\salary\\index.vue", "mtime": 1753168166378}, {"path": "C:\\Users\\<USER>\\Desktop\\定阔\\dingkuao\\定扩web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1713250439929}, {"path": "C:\\Users\\<USER>\\Desktop\\定阔\\dingkuao\\定扩web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1713250441304}, {"path": "C:\\Users\\<USER>\\Desktop\\定阔\\dingkuao\\定扩web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1713250440419}, {"path": "C:\\Users\\<USER>\\Desktop\\定阔\\dingkuao\\定扩web\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1713250439488}, {"path": "C:\\Users\\<USER>\\Desktop\\定阔\\dingkuao\\定扩web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1713250439495}, {"path": "C:\\Users\\<USER>\\Desktop\\定阔\\dingkuao\\定扩web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1713250440827}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5kcmF3ZXJfY29udGVudCB7DQogIG1hcmdpbjogMCAzMHB4Ow0KDQogIC5kcmF3ZXJfZm9vdGVyIHsNCiAgICBmbG9hdDogcmlnaHQ7DQogICAgcGFkZGluZy1ib3R0b206IDQwcnB4Ow0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsVA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/house/salary", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"10\" class=\"mb5\">\r\n      <el-col :span=\"1.5\">\r\n        <!-- <el-button\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['system:houseRoom:add']\"\r\n          >新增</el-button\r\n        > -->\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <!-- <el-button\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['system:houseRoom:edit']\"\r\n          >修改</el-button\r\n        > -->\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <!-- <el-button\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['system:houseRoom:remove']\"\r\n          >删除</el-button\r\n        > -->\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-dropdown\r\n          size=\"medium\"\r\n          @command=\"(command) => handleImportCommand(command)\"\r\n        >\r\n          <el-button plain icon=\"el-icon-caret-bottom\" size=\"mini\"\r\n            >数据导入</el-button\r\n          >\r\n          <el-dropdown-menu slot=\"dropdown\">\r\n            <el-dropdown-item command=\"handleImport\" icon=\"el-icon-upload\"\r\n              >数据导入</el-dropdown-item\r\n            >\r\n            <el-dropdown-item command=\"handleTemplete\" icon=\"el-icon-download\"\r\n              >下载模板</el-dropdown-item\r\n            >\r\n          </el-dropdown-menu>\r\n        </el-dropdown>\r\n        <div class=\"exl\"></div>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <!-- <el-button plain icon=\"el-icon-s-promotion\" :disabled=\"bankRecordList.length == 0\" size=\"mini\"\r\n          @click=\"dialogVisible = !dialogVisible\">发布</el-button> -->\r\n      </el-col>\r\n\r\n      <el-form\r\n        :model=\"queryParams\"\r\n        ref=\"queryForm\"\r\n        :inline=\"true\"\r\n        label-width=\"90px\"\r\n        class=\"el-form-search\"\r\n      >\r\n        <el-form-item label=\"姓名\" prop=\"nickName\" class=\"el-form-search-item\">\r\n          <el-input\r\n            v-model=\"queryParams.nickName\"\r\n            placeholder=\"请输入姓名\"\r\n          ></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"日期\" prop=\"month\" class=\"el-form-search-item\">\r\n          <el-date-picker\r\n            v-model=\"queryParams.month\"\r\n            type=\"month\"\r\n            value-format=\"yyyy-MM\"\r\n            placeholder=\"请选择日期\"\r\n          >\r\n          </el-date-picker>\r\n        </el-form-item>\r\n\r\n        <el-form-item class=\"el-form-search-item\">\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-search\"\r\n            size=\"mini\"\r\n            @click=\"handleQuery\"\r\n            >搜索</el-button\r\n          >\r\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n            >重置</el-button\r\n          >\r\n        </el-form-item>\r\n      </el-form>\r\n    </el-row>\r\n\r\n    <el-table\r\n      :height=\"tableHeight\"\r\n      v-loading=\"loading\"\r\n      :data=\"bankRecordList\"\r\n      ref=\"dataTable\"\r\n    >\r\n      <el-table-column label=\"#\" type=\"index\" width=\"50\" align=\"center\">\r\n        <template scope=\"scope\">\r\n          <span>{{\r\n            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1\r\n          }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column\r\n        v-for=\"(item, index) in titleList\"\r\n        :key=\"item.value\"\r\n        :label=\"item.label\"\r\n        align=\"center\"\r\n        :prop=\"item.value\"\r\n      >\r\n        <template slot-scope=\"{ row }\">\r\n          <span\r\n            v-if=\"\r\n              item.value === 'yestodayMoney' ||\r\n              item.value === 'incomeMoney' ||\r\n              item.value === 'expendMoney' ||\r\n              item.value === 'todayMoney'\r\n            \"\r\n          >\r\n            {{ row[item.value] }}\r\n          </span>\r\n          <span v-else>{{ row[item.value] }}</span>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getBankRecordList\"\r\n    />\r\n\r\n    <!-- 导入对话框 -->\r\n    <el-dialog :title=\"upload.title\" :visible.sync=\"upload.open\" width=\"400px\">\r\n      <el-upload\r\n        ref=\"upload\"\r\n        :limit=\"1\"\r\n        accept=\".xlsx, .xls\"\r\n        :headers=\"upload.headers\"\r\n        :action=\"upload.url\"\r\n        :disabled=\"upload.isUploading\"\r\n        :on-progress=\"handleFileUploadProgress\"\r\n        :on-success=\"handleFileSuccess\"\r\n        :auto-upload=\"false\"\r\n        drag\r\n      >\r\n        <i class=\"el-icon-upload\"></i>\r\n        <div class=\"el-upload__text\">\r\n          将文件拖到此处，或\r\n          <em>点击上传</em>\r\n        </div>\r\n        <div class=\"el-upload__tip\" slot=\"tip\">\r\n          <el-button\r\n            type=\"primary\"\r\n            plain\r\n            icon=\"el-icon-receiving\"\r\n            size=\"mini\"\r\n            @click=\"handleTemplete\"\r\n            style=\"float: right\"\r\n            >下载模板</el-button\r\n          >\r\n        </div>\r\n        <div class=\"el-upload__tip\" style=\"color: red\" slot=\"tip\">\r\n          提示：仅允许导入\"xls\"或\"xlsx\"格式文件！\r\n        </div>\r\n      </el-upload>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\r\n        <el-button @click=\"upload.open = false\">取 消</el-button>\r\n      </div>\r\n      <div class=\"exl\"></div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getListApi, exportListModel } from \"@/api/house/salary\";\r\nimport { titleList } from \"./titleList.js\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport { Export } from \"@/utils/Export\";\r\n\r\nexport default {\r\n  name: \"BankFund\",\r\n  dicts: [\"sys_notice_status\"],\r\n  data() {\r\n    return {\r\n      titleList,\r\n      baseURL: process.env.VUE_APP_BASE_API,\r\n      // 导入参数\r\n      upload: {\r\n        // 是否显示弹出层（用户导入）\r\n        open: false,\r\n        // 弹出层标题（用户导入）\r\n        title: \"\",\r\n        // 是否禁用上传\r\n        isUploading: false,\r\n        // 设置上传的请求头部\r\n        headers: { Authorization: \"Bearer \" + getToken() },\r\n        // 上传的地址\r\n        url: process.env.VUE_APP_BASE_API + \"/business/salary/upload\",\r\n      },\r\n      // 表格高度\r\n      tableHeight: document.documentElement.clientHeight - 280,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 表格数据\r\n      bankRecordList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        nickName: \"\",\r\n        month: \"\",\r\n      },\r\n      // 发布弹出层\r\n      dialogVisible: false,\r\n      // 发布项目组数组\r\n      deptIdList: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.getBankRecordList();\r\n  },\r\n  methods: {\r\n    /** 查询银行资金记录列表 */\r\n    getBankRecordList() {\r\n      this.loading = true;\r\n      getListApi(this.queryParams).then((response) => {\r\n        this.total = response.total;\r\n        this.bankRecordList = response.rows;\r\n        this.loading = false;\r\n      });\r\n    },\r\n\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getBankRecordList();\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n\r\n    /** 导入 */\r\n    handleImport() {\r\n      this.upload.title = \"导入\";\r\n      this.upload.url =\r\n        process.env.VUE_APP_BASE_API + \"/business/salary/upload\";\r\n      this.upload.open = true;\r\n    },\r\n\r\n    // 文件上传中处理\r\n    handleFileUploadProgress(event, file, fileList) {\r\n      this.upload.isUploading = true;\r\n    },\r\n\r\n    // 文件上传成功处理\r\n    handleFileSuccess(response, file, fileList) {\r\n      this.upload.open = false;\r\n      this.upload.isUploading = false;\r\n      this.$refs.upload.clearFiles();\r\n      this.$alert(response.msg, \"导入结果\", { dangerouslyUseHTMLString: true });\r\n      this.getBankRecordList();\r\n    },\r\n\r\n    // 提交上传文件\r\n    submitFileForm() {\r\n      this.$refs.upload.submit();\r\n    },\r\n\r\n    // 下载模板\r\n    handleTemplete() {\r\n      exportListModel()\r\n        .then((res) => {\r\n          const link = document.createElement(\"a\");\r\n          let blob = new Blob([res], { type: \"application/x-excel\" });\r\n          link.style.display = \"none\";\r\n          link.href = URL.createObjectURL(blob);\r\n          link.download = \"导入模板.xlsx\";\r\n          document.querySelector(\".exl\").appendChild(link);\r\n          link.click();\r\n          document.querySelector(\".exl\").removeChild(link);\r\n        })\r\n        .catch((error) => {\r\n          console.log(error);\r\n          this.$modal.msgError(\"下载模板失败，请重试\");\r\n        });\r\n    },\r\n\r\n    // 导出数据\r\n    handleExport() {\r\n      let data = {\r\n        title: \"薪资数据\",\r\n        url: \"/business/salary/export\",\r\n        token: getToken(),\r\n        method: \"get\",\r\n        data: this.queryParams,\r\n      };\r\n      Export(data);\r\n    },\r\n\r\n    // 导入操作触发\r\n    handleImportCommand(command) {\r\n      switch (command) {\r\n        case \"handleImport\":\r\n          this.handleImport();\r\n          break;\r\n        case \"handleTemplete\":\r\n          this.handleTemplete();\r\n          break;\r\n        case \"handleExport\":\r\n          this.handleExport();\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.drawer_content {\r\n  margin: 0 30px;\r\n\r\n  .drawer_footer {\r\n    float: right;\r\n    padding-bottom: 40rpx;\r\n  }\r\n}\r\n</style>\r\n"]}]}