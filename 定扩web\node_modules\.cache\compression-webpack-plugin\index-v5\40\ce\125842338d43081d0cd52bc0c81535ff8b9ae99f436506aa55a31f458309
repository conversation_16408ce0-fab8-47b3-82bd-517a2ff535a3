
061183e7d43acf08e582e1d258ed290e324939ea	{"key":"{\"nodeVersion\":\"v16.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"0.e95df06ef013ac980336.hot-update.js\",\"contentHash\":\"d4289ae18ae71b8fdc57b0baff881457\"}","integrity":"sha512-eyK1nZCE5FhvgrYuF2hq8k4QZH8ulke12vj6zezZn9Cx3WL/0xGzIGviLR6aL6XKIBcbNURDmGDQKFT9dt72hg==","time":1753843745065,"size":44886}