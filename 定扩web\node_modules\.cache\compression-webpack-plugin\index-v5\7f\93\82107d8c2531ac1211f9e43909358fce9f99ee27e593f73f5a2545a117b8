
3d21ada016663f5e836196c6753340c996307cc8	{"key":"{\"nodeVersion\":\"v16.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"753e5698460851e2d78d5a6dd35318a1\"}","integrity":"sha512-naNuBkk0gl2YGb0/9o1FgwmQ2S8FSPp8oocRnScN/WbcWYlinaiWatO+YSuIesWVcVp46RICUXoAZw7ghmyiyw==","time":1753843758142,"size":11434006}