import request from "@/utils/request";

// 查询表头列表
export function listHouseVillage(query) {
  return request({
    url: "/system/table/list",
    method: "get",
    params: query,
  });
}

// 查询项目组列表
export function deptList() {
  return request({
    url: "/system/userDept/all",
    method: "get",
  });
}

// 查询一级表头列表
export function listHeaders() {
  return request({
    url: "/system/dict/data/type/up_table_title",
    method: "get",
  });
}

// 查询房源小区详细
export function getHouseVillage(id) {
  return request({
    url: "/system/table/" + id,
    method: "get",
  });
}

// 新增表头列表
export function addHouseVillage(data) {
  return request({
    url: "/system/table",
    method: "post",
    data: data,
  });
}

// 修改表头
export function updateHouseVillage(data) {
  return request({
    url: "/system/table",
    method: "put",
    data: data,
  });
}

// 删除房源小区
export function delHouseVillage(id) {
  return request({
    url: "/system/table/" + id,
    method: "delete",
  });
}
