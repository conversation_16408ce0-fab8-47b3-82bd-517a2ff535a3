
6177a3c8749d78f602b42be16fb50206d8ae25b1	{"key":"{\"nodeVersion\":\"v16.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"0.9b84a3cabcf0d1634cf3.hot-update.js\",\"contentHash\":\"2f46d256932e548dba695215dd96ef1c\"}","integrity":"sha512-xH1qzvpgcsfFGaTN0G337t7j9TCA3TRCmrCSLT7FgqRM7LoNO7igITKs3rHctcVoghUTAlJJ5SL0lHIyV7XFiQ==","time":1753843769279,"size":44856}