
5893c2797c43364db6ba123de03a93e2e03c67cc	{"key":"{\"nodeVersion\":\"v16.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"0.72c18becc01193511d84.hot-update.js\",\"contentHash\":\"b36ac730ae7124c3b65ea26d824d9992\"}","integrity":"sha512-fp/UiLI6cKXdu9SA6mBGWGNsekjddoi4P+jdcenntB8+kWf3evU8GDVURLkcoZVt3JhKlGT2FSbigQNQZGHicg==","time":1753843953935,"size":3842}