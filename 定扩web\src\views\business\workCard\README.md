# 工作卡管理模块

## 功能概述

本模块实现了工作卡数据的管理功能，包括：

1. **工作卡列表页面** (`index.vue`)
   - 显示工作卡数据列表
   - 支持按部门、资源代码、名称搜索
   - 支持时间范围筛选
   - 支持分页显示
   - 支持数据导出
   - 支持上传行方数据

2. **数据上传页面** (`upload.vue`)
   - 独立的数据上传界面
   - 支持拖拽上传
   - 文件格式验证
   - 上传进度显示
   - 数据预览功能

## API 接口

### 1. 获取工作卡列表
- **接口地址**: `/business/workCard/list`
- **请求方式**: GET
- **请求参数**:
  ```javascript
  {
    pageNum: 1,        // 页码
    pageSize: 20,      // 每页数量
    deptName: "",      // 部门名称
    code: "",          // 资源代码
    name: "",          // 名称
    startTime: "",     // 开始时间
    endTime: ""        // 结束时间
  }
  ```
- **响应数据**:
  ```javascript
  {
    total: 100,        // 总数量
    rows: [            // 数据列表
      {
        id: 1,         // ID
        code: "CODE001", // 资源代码
        name: "名称",   // 名称
        deptName: "部门", // 部门
        yk: 100,       // 一卡
        yx: 95,        // 有效
        kj: 80,        // 快捷
        dk: 70,        // 动卡
        qw: 60,        // 企微
        kjRatio: 80.0, // 快捷比例
        dkRatio: 70.0, // 动卡比例
        qwRatio: 60.0, // 企微比例
        deptId: 1,     // 部门ID
        userId: 1,     // 用户ID
        day: "2024-01-01" // 最终时间
      }
    ]
  }
  ```

### 2. 上传行方数据
- **接口地址**: `/business/workCard/upload`
- **请求方式**: POST
- **请求参数**: FormData (文件上传)
- **响应数据**:
  ```javascript
  {
    code: 200,
    msg: "上传成功",
    data: {
      successCount: 100,  // 成功数量
      failCount: 0,       // 失败数量
      previewData: []     // 预览数据
    }
  }
  ```

### 3. 导出工作卡数据
- **接口地址**: `/business/workCard/export`
- **请求方式**: POST
- **请求参数**: 与列表查询参数相同
- **响应**: Excel 文件流

## 使用说明

### 1. 路由配置
需要在路由配置中添加以下路由：

```javascript
{
  path: '/business/workCard',
  component: () => import('@/views/business/workCard/index'),
  name: 'WorkCard',
  meta: { title: '工作卡管理', icon: 'card' }
},
{
  path: '/business/workCard/upload',
  component: () => import('@/views/business/workCard/upload'),
  name: 'WorkCardUpload',
  meta: { title: '上传行方数据', activeMenu: '/business/workCard' }
}
```

### 2. 菜单配置
在后台管理系统中添加相应的菜单项，确保用户有访问权限。

### 3. 后端接口
确保后端已实现对应的接口：
- `/business/workCard/list` - 获取工作卡列表
- `/business/workCard/upload` - 上传行方数据
- `/business/workCard/export` - 导出工作卡数据

## 文件结构

```
src/views/business/workCard/
├── index.vue          # 工作卡列表页面
├── upload.vue         # 数据上传页面
└── README.md          # 说明文档

src/api/business/
└── workCard.js        # 工作卡相关API接口
```

## 注意事项

1. 上传文件格式限制为 .xlsx 和 .xls
2. 文件大小限制为 10MB
3. 确保后端接口返回的数据格式与前端期望的格式一致
4. 比例字段会自动添加 % 符号显示
5. 时间字段使用 YYYY-MM-DD 格式

## 开发者信息

- 创建时间: 2024年
- 基于: Vue 2.6 + Element UI 2.15
- 依赖组件: Pagination（分页）、Upload（上传）
