<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>上传行方数据</span>
      </div>
      
      <div class="upload-container">
        <el-upload
          ref="upload"
          class="upload-demo"
          drag
          :action="uploadUrl"
          :headers="uploadHeaders"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :on-error="handleFileError"
          :before-upload="beforeUpload"
          :limit="1"
          :auto-upload="false"
          accept=".xlsx,.xls"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">
            <div>只能上传xlsx/xls文件，且不超过10MB</div>
            <div>请确保文件格式正确，包含所需的工作卡数据字段</div>
          </div>
        </el-upload>
        
        <div class="upload-actions">
          <el-button 
            type="primary" 
            @click="submitUpload" 
            :loading="uploadLoading"
            :disabled="!hasFile"
          >
            开始上传
          </el-button>
          <el-button @click="clearFiles">清空文件</el-button>
          <el-button type="info" @click="downloadTemplate">下载模板</el-button>
        </div>
      </div>

      <!-- 上传进度 -->
      <div v-if="uploadLoading" class="upload-progress">
        <el-progress :percentage="uploadProgress" :status="progressStatus"></el-progress>
        <p class="progress-text">{{ progressText }}</p>
      </div>

      <!-- 上传结果 -->
      <div v-if="uploadResult" class="upload-result">
        <el-alert
          :title="uploadResult.title"
          :type="uploadResult.type"
          :description="uploadResult.description"
          show-icon
          :closable="false"
        >
        </el-alert>
      </div>

      <!-- 数据预览 -->
      <div v-if="previewData.length > 0" class="data-preview">
        <h3>数据预览（前10条）</h3>
        <el-table :data="previewData" border style="width: 100%">
          <el-table-column prop="code" label="资源代码" width="120"></el-table-column>
          <el-table-column prop="name" label="名称" width="150"></el-table-column>
          <el-table-column prop="deptName" label="部门" width="120"></el-table-column>
          <el-table-column prop="yk" label="一卡" width="80"></el-table-column>
          <el-table-column prop="yx" label="有效" width="80"></el-table-column>
          <el-table-column prop="kj" label="快捷" width="80"></el-table-column>
          <el-table-column prop="dk" label="动卡" width="80"></el-table-column>
          <el-table-column prop="qw" label="企微" width="80"></el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script>
import { uploadWorkCardData } from "@/api/business/workCard";
import { getToken } from "@/utils/auth";

export default {
  name: "WorkCardUpload",
  data() {
    return {
      uploadUrl: process.env.VUE_APP_BASE_API + "/business/workCard/upload",
      uploadHeaders: { Authorization: "Bearer " + getToken() },
      uploadLoading: false,
      uploadProgress: 0,
      progressStatus: "",
      progressText: "",
      hasFile: false,
      uploadResult: null,
      previewData: [],
    };
  },
  methods: {
    /** 上传前检查 */
    beforeUpload(file) {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                     file.type === 'application/vnd.ms-excel';
      const isLt10M = file.size / 1024 / 1024 < 10;

      if (!isExcel) {
        this.$modal.msgError('只能上传 Excel 文件!');
        return false;
      }
      if (!isLt10M) {
        this.$modal.msgError('上传文件大小不能超过 10MB!');
        return false;
      }
      
      this.hasFile = true;
      this.uploadResult = null;
      this.previewData = [];
      return false; // 阻止自动上传
    },

    /** 文件上传中处理 */
    handleFileUploadProgress(event) {
      this.uploadLoading = true;
      this.uploadProgress = Math.round((event.loaded / event.total) * 100);
      this.progressText = `正在上传... ${this.uploadProgress}%`;
      
      if (this.uploadProgress === 100) {
        this.progressText = "上传完成，正在处理数据...";
        this.progressStatus = "success";
      }
    },

    /** 文件上传成功处理 */
    handleFileSuccess(response, file) {
      this.uploadLoading = false;
      this.uploadProgress = 100;
      this.progressStatus = "success";
      
      if (response.code === 200) {
        this.uploadResult = {
          title: "上传成功",
          type: "success",
          description: `成功上传 ${response.data?.successCount || 0} 条数据${response.data?.failCount ? `，失败 ${response.data.failCount} 条` : ''}`
        };
        
        // 如果有预览数据，显示预览
        if (response.data?.previewData) {
          this.previewData = response.data.previewData.slice(0, 10);
        }
        
        this.$modal.msgSuccess("数据上传成功");
        
        // 3秒后跳转到列表页面
        setTimeout(() => {
          this.$router.push('/business/workCard');
        }, 3000);
      } else {
        this.uploadResult = {
          title: "上传失败",
          type: "error",
          description: response.msg || "上传过程中发生错误"
        };
        this.$modal.msgError(response.msg || "上传失败");
      }
      
      this.progressText = "";
    },

    /** 文件上传失败处理 */
    handleFileError(error) {
      this.uploadLoading = false;
      this.uploadProgress = 0;
      this.progressStatus = "exception";
      this.progressText = "";
      
      this.uploadResult = {
        title: "上传失败",
        type: "error",
        description: "网络错误或服务器异常，请稍后重试"
      };
      
      this.$modal.msgError("上传失败，请重试");
    },

    /** 提交上传文件 */
    submitUpload() {
      if (!this.hasFile) {
        this.$modal.msgWarning("请先选择要上传的文件");
        return;
      }
      this.$refs.upload.submit();
    },

    /** 清空文件 */
    clearFiles() {
      this.$refs.upload.clearFiles();
      this.hasFile = false;
      this.uploadResult = null;
      this.previewData = [];
      this.uploadProgress = 0;
      this.progressText = "";
      this.progressStatus = "";
    },

    /** 下载模板 */
    downloadTemplate() {
      // 创建一个简单的模板下载
      const templateData = [
        ['资源代码', '名称', '部门', '一卡', '有效', '快捷', '动卡', '企微', '快捷比例', '动卡比例', '企微比例', '最终时间'],
        ['CODE001', '示例名称', '示例部门', 100, 95, 80, 70, 60, 80.0, 70.0, 60.0, '2024-01-01']
      ];
      
      // 这里可以调用后端接口下载真实模板
      this.$modal.msgInfo("模板下载功能需要后端支持，请联系管理员获取模板文件");
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.box-card {
  max-width: 800px;
  margin: 0 auto;
}

.upload-container {
  text-align: center;
  
  .upload-demo {
    margin-bottom: 20px;
  }
  
  .upload-actions {
    margin-top: 20px;
    
    .el-button {
      margin: 0 10px;
    }
  }
}

.upload-progress {
  margin: 20px 0;
  
  .progress-text {
    margin-top: 10px;
    color: #606266;
    font-size: 14px;
  }
}

.upload-result {
  margin: 20px 0;
}

.data-preview {
  margin-top: 30px;
  
  h3 {
    margin-bottom: 15px;
    color: #303133;
  }
}
</style>
