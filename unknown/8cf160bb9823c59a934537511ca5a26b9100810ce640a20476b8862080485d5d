<template>
  <div class="channel-container">
    <!-- 搜索表单 -->
    <el-form :inline="true" :model="searchForm" class="search-form">
      <el-form-item label="渠道名称">
        <el-input
          v-model="searchForm.name"
          placeholder="请输入渠道名称"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区 -->
    <div class="operation-area">
      <el-button type="primary" plain @click="handleAdd">新增渠道</el-button>
    </div>

    <!-- 表格 -->
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column prop="name" label="渠道名称" align="center" />
      <el-table-column
        prop="refund"
        label="客户机票退票比例(%)"
        align="center"
      />
      <el-table-column
        prop="mileage"
        label="退票里程消耗比(%)"
        align="center"
      />
      <el-table-column prop="tax" label="退票税费比例(%)" align="center" />

      <el-table-column
        prop="airportRule"
        label="机票价格计算规则"
        align="center"
      />

      <el-table-column label="操作" width="150" align="center">
        <template #default="scope">
          <el-button type="text" size="small" @click="handleEdit(scope.row)"
            >编辑</el-button
          >
          <el-button type="text" size="small" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 在表格组件后添加分页组件 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
        :current-page="pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>

    <!-- 新增/编辑弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="700px">
      <el-form
        :model="formData"
        :rules="rules"
        ref="formRef"
        label-width="140px"
        :inline="true"
        class="dialog-form"
      >
        <el-form-item label="渠道名称" prop="name" class="full-width">
          <el-input v-model="formData.name" />
        </el-form-item>

        <el-form-item label="客户机票退票比例%" prop="refund">
          <el-input-number
            v-model="formData.refund"
            :min="0"
            :max="100"
            :precision="2"
          >
          </el-input-number>
        </el-form-item>

        <el-form-item label="退票里程消耗比%" prop="mileage">
          <el-input-number
            v-model="formData.mileage"
            :min="0"
            :max="100"
            :precision="2"
          >
          </el-input-number>
        </el-form-item>

        <el-form-item label="退票税费比例%" prop="tax">
          <el-input-number
            v-model="formData.tax"
            :min="0"
            :max="100"
            :precision="2"
          >
          </el-input-number>
        </el-form-item>

        <el-form-item
          label="机票价格计算规则"
          prop="airportRule"
          class="full-width"
        >
          <el-input v-model="formData.airportRule" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  getChannelList,
  addChannel,
  updateChannel,
  deleteChannel,
} from "@/api/ticket/channel";

export default {
  name: "ChannelManagement",
  data() {
    return {
      // 搜索表单数据
      searchForm: {
        name: "",
      },
      // 表格数据
      tableData: [],
      // 弹窗显示状态
      dialogVisible: false,
      // 弹窗标题
      dialogTitle: "新增渠道",
      // 表单数据
      formData: {
        name: "",
        refund: 0,
        mileage: 0,
        tax: 0,
        airportRule: "",
      },
      // 表单校验规则
      rules: {
        name: [{ required: true, message: "请输入渠道名称", trigger: "blur" }],
        refund: [
          { required: true, message: "请输入退票比例", trigger: "blur" },
        ],
        mileage: [
          { required: true, message: "请输入里程消耗比", trigger: "blur" },
        ],
        tax: [{ required: true, message: "请输入税费比例", trigger: "blur" }],
      },
      // 分页相关数据
      pageNum: 1,
      pageSize: 10,
      total: 0,
    };
  },
  methods: {
    // 获取表格数据
    async getTableData() {
      try {
        const params = {
          ...this.searchForm,
          pageNum: this.pageNum,
          pageSize: this.pageSize,
        };
        const res = await getChannelList(params);
        if (res.code === 200) {
          this.tableData = res.rows;
          this.total = res.total;
        } else {
          this.$message.error(res.msg || "获取数据失败");
        }
      } catch (error) {
        console.error("获取表格数据失败:", error);
        this.$message.error("获取数据失败");
      }
    },

    // 搜索
    handleSearch() {
      this.pageNum = 1;
      this.getTableData();
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        name: "",
      };
      this.pageNum = 1;
      this.getTableData();
    },

    // 新增
    handleAdd() {
      this.dialogTitle = "新增渠道";
      this.dialogVisible = true;
      this.formData = {
        name: "",
        refund: 0,
        mileage: 0,
        tax: 0,
        airportRule: "",
      };
    },

    // 编辑
    handleEdit(row) {
      this.dialogTitle = "编辑渠道";
      this.dialogVisible = true;
      this.formData = { ...row };
    },

    // 删除
    handleDelete(row) {
      this.$confirm("确认删除该渠道吗？", "提示", {
        type: "warning",
      })
        .then(async () => {
          try {
            const res = await deleteChannel(row.id);
            if (res.code === 200) {
              this.$message.success("删除成功");
              this.getTableData();
            } else {
              this.$message.error(res.msg || "删除失败");
            }
          } catch (error) {
            console.error("删除失败:", error);
            this.$message.error("删除失败");
          }
        })
        .catch(() => {});
    },

    // 提交表单
    handleSubmit() {
      this.$refs.formRef.validate(async (valid) => {
        if (valid) {
          try {
            const isEdit = this.formData.id !== undefined;
            const res = await (isEdit ? updateChannel : addChannel)(
              this.formData
            );

            if (res.code === 200) {
              this.$message.success(isEdit ? "修改成功" : "添加成功");
              this.dialogVisible = false;
              this.getTableData();
            } else {
              this.$message.error(
                res.msg || (isEdit ? "修改失败" : "添加失败")
              );
            }
          } catch (error) {
            console.error(isEdit ? "修改失败:" : "添加失败:", error);
            this.$message.error(isEdit ? "修改失败" : "添加失败");
          }
        }
      });
    },

    // 添加处理分页变化的方法
    handlePageChange(page) {
      this.pageNum = page;
      this.getTableData();
    },

    // 添加处理每页显示条数变化的方法
    handleSizeChange(size) {
      this.pageSize = size;
      this.pageNum = 1; // 重置为第一页
      this.getTableData();
    },
  },
  created() {
    // 页面创建时获取表格数据
    this.getTableData();
  },
};
</script>

<style lang="scss" scoped>
.channel-container {
  padding: 20px;

  .search-form {
    margin-bottom: 16px;
  }

  .operation-area {
    margin-bottom: 16px;
  }

  .dialog-form {
    .el-form-item {
      width: 45%;
      margin-right: 4%;

      &.full-width {
        width: 94%;
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}

/* 深度选择器在Vue2中使用 >>> 或 ::v-deep */
.dialog-form ::v-deep .el-form-item__content {
  width: calc(100% - 140px);

  .el-input-number {
    width: 100%;
  }
}

/* 或者使用这种写法 */
/* .dialog-form >>> .el-form-item__content {
  width: calc(100% - 140px);

  .el-input-number {
    width: 100%;
  }
} */
</style>
