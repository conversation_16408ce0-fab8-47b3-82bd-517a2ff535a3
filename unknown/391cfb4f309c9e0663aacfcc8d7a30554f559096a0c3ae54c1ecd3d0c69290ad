<template>
  <div class="app-container">
    <el-row :gutter="24" type="flex" align="center">
      <el-col :span="18">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          class="el-form-search"
        >
          <el-form-item
            label="姓名"
            prop="nickName"
            class="el-form-search-item"
          >
            <el-input
              v-model="queryParams.nickName"
              placeholder="请输入姓名"
              clearable
              size="mini"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <!-- <el-form-item label="手机号" prop="phonenumber" class="el-form-search-item">
            <el-input v-model="queryParams.phonenumber" placeholder="请输入手机号" clearable size="mini"
              @keyup.enter.native="handleQuery" />
          </el-form-item> -->
          <el-form-item
            label="部门"
            prop="deptName"
            class="el-form-search-item"
          >
            <el-input
              v-model="queryParams.deptName"
              placeholder="请输入部门名"
              clearable
              size="mini"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="请选择月份">
            <el-date-picker
              v-model="queryParams.time"
              type="month"
              value-format="yyyy-MM"
              placeholder="选择月"
            >
            </el-date-picker>

            <!-- <el-date-picker class="picker-width" v-model="timeDate" type="daterange" align="right" unlink-panels
              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="pickerOptions"
              value-format="yyyy-MM-dd" @change="change">
            </el-date-picker> -->
          </el-form-item>
          <el-form-item
            label="银行"
            prop="bankName"
            class="el-form-search-item"
          >
            <el-select
              v-model="queryParams.bankName"
              placeholder="请选择银行"
              clearable
              size="mini"
            >
              <el-option
                v-for="bank in bankList"
                :key="bank.value"
                :label="bank.label"
                :value="bank.label"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="6"> </el-col>
      <el-col :span="2">
        <el-button
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:article:export']"
          >导出</el-button
        >
      </el-col>
    </el-row>

    <el-table
      :height="tableHeight"
      v-loading="loading"
      :tree-props="{ children: 'children' }"
      row-key="id"
      :data="articleList"
      :stripe="true"
    >
      <!-- <el-table-column type="selection" width="50" align="center" /> -->
      <el-table-column fixed label="#" type="index" width="50" align="center">
        <template scope="scope">
          <span>{{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}</span>
        </template>
      </el-table-column>

      <el-table-column
        v-for="(item, index) in articleLists"
        :fixed="index < 7"
        :key="item.value"
        :label="item.label"
        align="center"
        :prop="item.value"
      >
      </el-table-column>

      <!-- <el-table-column label="银行" align="center" prop="bankName">
        <template slot-scope="scope">
          <div style="display: flex; justify-content: center; align-items: center;">
            <img :src="require('@/assets/images/' + scope.row.bankName + '.png')" width="20px"
              :alt="scope.row.bankName" />
            <div style="margin-left: 10px;">
              {{ scope.row.bankName }}
            </div>
          </div>
        </template>
      </el-table-column> -->

      <!-- <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:article:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:article:remove']">删除</el-button>
        </template>
      </el-table-column> -->
    </el-table>
    <div class="exl"></div>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改内容管理对话框 -->
    <!-- <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body :close-on-click-modal="false"
      v-dialogDrag>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="类型" prop="articleType">
          <el-select v-model="form.articleType" placeholder="请选择类型" style="width: 100%">
            <el-option v-for="dict in dict.type.sys_article_type" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="标题" prop="smallTitle">
          <el-input v-model="form.smallTitle" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="简介" prop="bigTitle">
          <el-input v-model="form.bigTitle" type="textarea" placeholder="请输入简介" />
        </el-form-item>
        <el-form-item label="封面图" prop="faceUrl">
          <image-upload v-model="form.faceUrl" :limit="1" />
        </el-form-item>
        <el-form-item label="内容">
          <editor v-model="form.articleContent" :min-height="192" />
        </el-form-item>
        <el-form-item label="来源" prop="articleSource">
          <el-select v-model="form.articleSource" placeholder="请选择来源" style="width: 100%">
            <el-option v-for="dict in dict.type.sys_article_source" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="热度值" prop="sortNo">
          <el-input v-model="form.sortNo" placeholder="请输入热度值" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog> -->
  </div>
</template>

<script>
import {
  listArticle,
  // getArticle,
  delArticle,
  getexl,
  listArticles,
  // addArticle,
  // updateArticle,
} from "@/api/system/article";
import axios from "axios";
import { getToken } from "@/utils/auth";
export default {
  name: "Article",
  // dicts: ["sys_article_type", "sys_article_source"],
  data() {
    return {
      baseURL: process.env.VUE_APP_BASE_API,
      // 表格高度
      tableHeight: document.documentElement.clientHeight - 280,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 内容管理表格数据
      articleList: [],
      // 内容管理表头数据
      articleLists: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        nickName: "",
        phonenumber: "",
        deptName: "",
        time: "",
      },
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      bankList: [
        {
          label: "中信",
          value: 1,
        },
        {
          label: "广发",
          value: 2,
        },
        {
          label: "浦发",
          value: 3,
        },
      ],
      // 表单参数
      // form: {},
      // 表单校验
      // rules: {
      //   articleType: [
      //     { required: true, message: "类型不能为空", trigger: "change" },
      //   ],
      //   smallTitle: [
      //     { required: true, message: "标题不能为空", trigger: "blur" },
      //   ],
      //   faceUrl: [{ required: true, message: "封面不能为空", trigger: "blur" }],
      // },
    };
  },
  created() {
    this.getLists();
    this.getList();
  },
  methods: {
    /** 查询内容管理表头 */
    getLists() {
      listArticles(this.queryParams).then((response) => {
        console.log(response);
        this.articleLists = response.data.map((item) => {
          // 如果item.label包含日就切掉前三位 再把日替换成号

          if (item.label.includes("月")) {
            item.label = item.label.slice(3);
            //再把日替换成号
            item.label = item.label.replace("日", "号");
          }

          return item;
        });
      });
    },
    /** 查询内容管理列表 */
    getList() {
      this.loading = true;
      listArticle(this.queryParams).then((response) => {
        // 如果包含子集就把子集的userId替换成唯一索引

        response.rows.forEach((item) => {
          item.userType == "22"
            ? (item.userType = "主管")
            : (item.userType = "业务员");
          if (item.children) {
            item.children.forEach((item2, index) => {
              item2.nickName = "";
              item2.phonenumber = "";
              item2.deptName = "";
              item2.userType = "";
            });
          }
        });

        this.articleList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        articleType: null,
        smallTitle: null,
        bigTitle: null,
        faceUrl: null,
        faceThum: null,
        articleContent: null,
        articleSource: null,
        sortNo: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
        remark: null,
      };
      this.resetForm("form");
    },

    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.time = "";
      this.handleQuery();
    },
    // 多选框选中数据
    // handleSelectionChange(selection) {
    //   this.ids = selection.map((item) => item.id);
    //   this.single = selection.length !== 1;
    //   this.multiple = !selection.length;
    // },
    /** 新增按钮操作 */
    // handleAdd() {
    //   this.reset();
    //   this.open = true;
    //   this.title = "添加文章";
    // },
    /** 修改按钮操作 */
    // handleUpdate(row) {
    //   this.reset();
    //   const id = row.id || this.ids;
    //   getArticle(id).then((response) => {
    //     this.form = response.data;
    //     this.open = true;
    //     this.title = "修改文章";
    //   });
    // },
    /** 提交按钮 */
    // submitForm() {
    //   this.$refs["form"].validate((valid) => {
    //     if (valid) {
    //       if (this.form.id != null) {
    //         updateArticle(this.form).then((response) => {
    //           this.$modal.msgSuccess("修改成功");
    //           this.open = false;
    //           this.getList();
    //         });
    //       } else {
    //         addArticle(this.form).then((response) => {
    //           this.$modal.msgSuccess("新增成功");
    //           this.open = false;
    //           this.getList();
    //         });
    //       }
    //     }
    //   });
    // },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm("是否确认删除记录？")
        .then(function () {
          return delArticle(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */

    exportMethod(data) {
      axios({
        method: "post",
        headers: {
          Authorization: "Bearer " + getToken(),
        },
        url: this.baseURL + "/system/user/workDataExport",
        data: data,
        responseType: "blob",
      })
        .then((res) => {
          const link = document.createElement("a");
          let blob = new Blob([res.data], { type: "application/x-excel" });
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          link.download = this.queryParams.time
            ? `${this.queryParams.time}提交记录.xlsx`
            : "提交记录.xlsx";
          document.querySelector(".exl").appendChild(link);
          link.click();
        })
        .catch((error) => {
          this.$Notice.error({
            title: "错误",
            desc: "网络连接错误",
          });
        });
    },

    handleExport() {
      this.exportMethod(this.queryParams);
    },
  },
};
</script>
<style>
.el-form-search {
  float: none;
}

.picker-width {
  width: 150%;
}
</style>
