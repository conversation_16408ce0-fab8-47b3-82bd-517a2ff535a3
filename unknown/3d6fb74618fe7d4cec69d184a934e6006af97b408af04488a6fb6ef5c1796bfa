<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb5">
      <el-col :span="1.5">
        <!-- <el-button
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:houseRoom:add']"
          >新增</el-button
        > -->
      </el-col>
      <el-col :span="1.5">
        <!-- <el-button
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:houseRoom:edit']"
          >修改</el-button
        > -->
      </el-col>
      <el-col :span="1.5">
        <!-- <el-button
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:houseRoom:remove']"
          >删除</el-button
        > -->
      </el-col>
      <el-col :span="1.5">
        <el-dropdown
          size="medium"
          @command="(command) => handleImportCommand(command)"
        >
          <el-button plain icon="el-icon-caret-bottom" size="mini"
            >数据导入</el-button
          >
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="handleImport" icon="el-icon-upload"
              >数据导入</el-dropdown-item
            >
            <el-dropdown-item command="handleTemplete" icon="el-icon-download"
              >下载模板</el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown>
        <div class="exl"></div>
      </el-col>
      <el-col :span="1.5">
        <!-- <el-button plain icon="el-icon-s-promotion" :disabled="accountSettleList.length == 0" size="mini"
          @click="dialogVisible = !dialogVisible">发布</el-button> -->
      </el-col>

      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        label-width="80px"
        class="el-form-search"
      >
        <el-form-item label="项目组" prop="dept" class="el-form-search-item">
          <el-input
            v-model="queryParams.dept"
            placeholder="请输入项目组"
          ></el-input>
        </el-form-item>
        <el-form-item label="地区" prop="address" class="el-form-search-item">
          <el-input
            v-model="queryParams.address"
            placeholder="请输入地区"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="回款情况"
          prop="returnType"
          class="el-form-search-item"
        >
          <el-select
            v-model="queryParams.returnType"
            placeholder="请选择回款情况"
            clearable
          >
            <el-option label="已结算" value="已结算"></el-option>
            <el-option label="未结算" value="未结算"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="月份" prop="month" class="el-form-search-item">
          <el-date-picker
            :clearable="false"
            v-model="queryParams.month"
            format="yyyy 年 MM 月"
            value-format="yyyy-MM"
            type="month"
            placeholder="请选择月份"
          >
          </el-date-picker>
        </el-form-item>

        <el-form-item class="el-form-search-item">
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
    </el-row>

    <el-table
      :height="tableHeight"
      v-loading="loading"
      :data="accountSettleList"
      ref="dataTable"
    >
      <el-table-column label="#" type="index" width="50" align="center">
        <template scope="scope">
          <span>{{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}</span>
        </template>
      </el-table-column>

      <el-table-column
        v-for="(item, index) in titleList"
        :key="item.value"
        :label="item.label"
        align="center"
        :prop="item.value"
      >
        <template slot-scope="{ row }">
          <span v-if="item.value === 'money'">{{
            row[item.value].toFixed(2)
          }}</span>
          <span v-else>{{ row[item.value] }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="120">
        <template slot-scope="{ row }">
          <el-button
            v-if="row.returnType === '未结算'"
            size="mini"
            type="primary"
            @click="handleSettle(row)"
            >结算</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getAccountSettleList"
    />

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px">
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" slot="tip">
          <el-button
            type="primary"
            plain
            icon="el-icon-receiving"
            size="mini"
            @click="handleTemplete"
            style="float: right"
            >下载模板</el-button
          >
        </div>
        <div class="el-upload__tip" style="color: red" slot="tip">
          提示：仅允许导入"xls"或"xlsx"格式文件！
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
      <div class="exl"></div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getAccountSettleList,
  exportAccountSettleModel,
  importAccountSettleData,
  settle,
} from "@/api/business/accountSettle";
import { titleList } from "./titleList.js";
import { getToken } from "@/utils/auth";
import { Export } from "@/utils/Export";

export default {
  name: "BusinessAnalysis1",
  dicts: ["sys_notice_status"],
  data() {
    return {
      titleList,
      baseURL: process.env.VUE_APP_BASE_API,
      // 导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/business/settle/upload",
      },
      // 表格高度
      tableHeight: document.documentElement.clientHeight - 280,
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      accountSettleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        dept: "",
        address: "",
        returnType: "",
        month: "",
      },
      // 发布弹出层
      dialogVisible: false,
      // 发布项目组数组
      deptIdList: [],
    };
  },
  created() {
    this.getAccountSettleList();
  },
  methods: {
    /** 查询账户结算列表 */
    getAccountSettleList() {
      this.loading = true;
      getAccountSettleList(this.queryParams).then((response) => {
        this.total = response.total;
        this.accountSettleList = response.rows;
        this.loading = false;
      });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getAccountSettleList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 导入 */
    handleImport() {
      this.upload.title = "导入账户结算数据";
      this.upload.url =
        process.env.VUE_APP_BASE_API + "/business/settle/upload";
      this.upload.open = true;
    },

    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },

    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getAccountSettleList();
    },

    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },

    // 下载模板
    handleTemplete() {
      exportAccountSettleModel()
        .then((res) => {
          const link = document.createElement("a");
          let blob = new Blob([res], { type: "application/x-excel" });
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          link.download = "账户结算导入模板.xlsx";
          document.querySelector(".exl").appendChild(link);
          link.click();
          document.querySelector(".exl").removeChild(link);
        })
        .catch((error) => {
          console.log(error);
          this.$modal.msgError("下载模板失败，请重试");
        });
    },

    // 导出数据
    handleExport() {
      let data = {
        title: "账户结算数据",
        url: "/business/settle/export",
        token: getToken(),
        method: "get",
        data: this.queryParams,
      };
      Export(data);
    },

    // 导入操作触发
    handleImportCommand(command) {
      switch (command) {
        case "handleImport":
          this.handleImport();
          break;
        case "handleTemplete":
          this.handleTemplete();
          break;
        case "handleExport":
          this.handleExport();
          break;
        default:
          break;
      }
    },

    // 结算操作
    handleSettle(row) {
      this.$confirm("确认对该记录进行结算操作?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          settle({ id: row.id })
            .then((response) => {
              if (response.code === 200) {
                this.$modal.msgSuccess("结算成功");
                this.getAccountSettleList();
              } else {
                this.$modal.msgError(response.msg);
              }
            })
            .catch((error) => {
              console.log(error);
              this.$modal.msgError("结算失败，请重试");
            });
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.drawer_content {
  margin: 0 30px;

  .drawer_footer {
    float: right;
    padding-bottom: 40rpx;
  }
}
</style>
