<template>
  <div class="wish-list-container">
    <!-- 筛选表单 -->
    <div class="filter-form">
      <el-form :model="queryParams" ref="queryForm" :inline="true">
        <el-form-item label="月份" prop="moth">
          <el-date-picker
            v-model="queryParams.moth"
            type="month"
            placeholder="请选择月份"
            value-format="yyyy-MM"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
          <el-button type="warning" @click="handleExport">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="wishList" style="width: 100%">
      <el-table-column prop="moth" label="月份" />
      <el-table-column prop="wish" label="愿望" />
      <el-table-column prop="nickName" label="用户名称" />
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getWishList, exportWish } from "@/api/wishing";
import Pagination from "@/components/Pagination";

export default {
  name: "WishList",
  components: {
    Pagination,
  },
  data() {
    return {
      // 加载状态
      loading: false,
      // 导出加载
      exportLoading: false,
      // 愿望列表
      wishList: [],
      // 总条数
      total: 0,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        moth: "",
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 获取列表数据
    async getList() {
      this.loading = true;
      try {
        const res = await getWishList(this.queryParams);
        this.wishList = res.rows;
        this.total = res.total;
      } catch (error) {
        console.error("获取数据失败:", error);
        this.$message.error("获取数据失败");
      } finally {
        this.loading = false;
      }
    },

    // 查询按钮
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    // 重置按钮
    resetQuery() {
      this.$refs.queryForm.resetFields();
      this.queryParams.pageNum = 1;
      this.getList();
    },

    // 导出按钮
    async handleExport() {
      try {
        this.exportLoading = true;
        const res = await exportWish(this.queryParams);
        // 创建a标签下载文件
        const blob = new Blob([res], { type: "application/vnd.ms-excel" });
        const link = document.createElement("a");
        link.href = window.URL.createObjectURL(blob);
        link.download = "愿望列表.xlsx";
        link.click();
        this.$message.success("导出成功");
      } catch (error) {
        console.error("导出失败:", error);
        this.$message.error("导出失败");
      } finally {
        this.exportLoading = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.wish-list-container {
  padding: 20px;

  .filter-form {
    margin-bottom: 20px;
  }
}
</style>
