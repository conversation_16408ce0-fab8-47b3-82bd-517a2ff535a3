<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb5">
      <el-col :span="1.5">
        <!-- <el-button
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:houseRoom:add']"
          >新增</el-button
        > -->
      </el-col>
      <el-col :span="1.5">
        <!-- <el-button
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:houseRoom:edit']"
          >修改</el-button
        > -->
      </el-col>
      <el-col :span="1.5">
        <!-- <el-button
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:houseRoom:remove']"
          >删除</el-button
        > -->
      </el-col>
      <el-col :span="1.5">
        <el-dropdown
          size="medium"
          @command="(command) => handleImportCommand(command)"
          v-hasPermi="['system:houseRoom:export']"
        >
          <el-button plain icon="el-icon-caret-bottom" size="mini"
            >数据导入</el-button
          >
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="handleImport" icon="el-icon-upload"
              >数据导入</el-dropdown-item
            >
            <el-dropdown-item command="handleTemplete" icon="el-icon-download"
              >下载模板</el-dropdown-item
            >
            <!-- <el-dropdown-item command="handleExport" icon="el-icon-download">数据导出</el-dropdown-item> -->
          </el-dropdown-menu>
        </el-dropdown>
        <div class="exl"></div>
      </el-col>
      <el-col :span="1.5">
        <!-- <el-button plain icon="el-icon-s-promotion" :disabled="houseRoomList.length == 0" size="mini"
          @click="dialogVisible = !dialogVisible">发布</el-button> -->
      </el-col>
      <!-- 发布弹窗 -->
      <!-- <el-dialog title="请选择项目组进行发布" :visible.sync="dialogVisible" width="30%" @close="deptIdList = []">
        <el-select v-model="deptIdList" multiple placeholder="请选择项目组">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="publish">确 定</el-button>
        </span>
      </el-dialog> -->

      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        label-width="70px"
        class="el-form-search"
      >
        <el-form-item
          label="项目组"
          prop="deptName"
          class="el-form-search-item"
        >
          <el-input
            v-model="queryParams.deptName"
            placeholder="请输入项目组名称"
          ></el-input>
        </el-form-item>

        <el-form-item label="月份" prop="month" class="el-form-search-item">
          <el-date-picker
            :clearable="false"
            v-model="queryParams.month"
            format="yyyy 年 MM 月"
            value-format="yyyy-MM"
            type="month"
            placeholder="请选择月份"
          >
          </el-date-picker>
        </el-form-item>

        <el-form-item class="el-form-search-item">
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
    </el-row>

    <el-table
      :height="tableHeight"
      v-loading="loading"
      :data="monthThirdList"
      ref="dataTable"
    >
      <el-table-column label="#" type="index" width="50" align="center">
        <template scope="scope">
          <span>{{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}</span>
        </template>
      </el-table-column>

      <el-table-column
        v-for="(item, index) in titleList"
        :key="item.value"
        :label="item.label"
        align="center"
        :prop="item.value"
      >
        <template slot-scope="{ row }">
          <span
            v-if="item.value === 'redPrice' || item.value === 'cardPrice'"
            >{{ row[item.value].toFixed(2) }}</span
          >
          <span v-else-if="item.label === '银行'">
            {{
              row[item.value] === 1
                ? "中信"
                : row[item.value] === 2
                ? "广发"
                : row[item.value] === 3
                ? "浦发"
                : ""
            }}
          </span>
          <span v-else>{{ row[item.value] }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getMonthThirdData"
    />

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px">
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" slot="tip">
          <el-button
            type="primary"
            plain
            icon="el-icon-receiving"
            size="mini"
            @click="handleTemplete"
            style="float: right"
            >下载模板</el-button
          >
        </div>
        <div class="el-upload__tip" style="color: red" slot="tip">
          提示：仅允许导入"xls"或"xlsx"格式文件！
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
      <div class="exl"></div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getMonthThirdList,
  exportMonthThirdModel,
} from "@/api/business/monthThird";
import { titleList } from "./titleList.js";
import { getToken } from "@/utils/auth";
import { Export } from "@/utils/Export";
import axios from "axios";

export default {
  name: "M3List",
  dicts: ["sys_notice_status"],
  data() {
    return {
      titleList,
      baseURL: process.env.VUE_APP_BASE_API,
      // 导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/business/monthThird/upload",
      },
      // 表格高度
      tableHeight: document.documentElement.clientHeight - 280,
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      monthThirdList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        deptName: "",
        month: "",
      },
      // 项目组列表
      options: [
        {
          label: "中信",
          value: "1",
        },
        {
          label: "广发",
          value: "2",
        },
        {
          label: "浦发",
          value: "3",
        },
      ],
      // 发布弹出层
      dialogVisible: false,
      // 发布项目组数组
      deptIdList: [],
    };
  },
  created() {
    this.getMonthThirdData();
  },
  methods: {
    /** 查询M3核销统计列表 */
    getMonthThirdData() {
      this.loading = true;
      getMonthThirdList(this.queryParams).then((response) => {
        this.total = response.total;
        this.monthThirdList = response.rows;
        this.loading = false;
      });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getMonthThirdData();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 导入 */
    handleImport() {
      this.upload.title = "导入M3核销统计";
      this.upload.url =
        process.env.VUE_APP_BASE_API + "/business/monthThird/upload";
      this.upload.open = true;
    },

    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },

    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getMonthThirdData();
    },

    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },

    // 下载模板
    handleTemplete() {
      exportMonthThirdModel()
        .then((res) => {
          const link = document.createElement("a");
          let blob = new Blob([res], { type: "application/x-excel" });
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          link.download = "M3核销统计导入模板.xlsx";
          document.querySelector(".exl").appendChild(link);
          link.click();
          document.querySelector(".exl").removeChild(link);
        })
        .catch((error) => {
          console.log(error);
          this.$modal.msgError("下载模板失败，请重试");
        });
    },

    // 导出数据
    handleExport() {
      let data = {
        title: "M3核销统计数据",
        url: "/business/monthThird/export",
        token: getToken(),
        method: "get",
        data: this.queryParams,
      };
      Export(data);
    },

    // 导入操作触发
    handleImportCommand(command) {
      switch (command) {
        case "handleImport":
          this.handleImport();
          break;
        case "handleTemplete":
          this.handleTemplete();
          break;
        case "handleExport":
          this.handleExport();
          break;
        default:
          break;
      }
    },

    // 重新绘制表格，防止抖动与滚动条异常
    setDataTable() {
      this.$nextTick(() => {
        this.$refs.dataTable.doLayout();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.drawer_content {
  margin: 0 30px;

  .drawer_footer {
    float: right;
    padding-bottom: 40rpx;
  }
}
</style>
