<template>
  <div class="app-container">
    <el-row :gutter="24" align="center" justify="center">
      <el-col :span="4">
        <el-dropdown
          size="medium"
          @command="(command) => handleImportCommand(command)"
          v-hasPermi="['system:houseRoom:export']"
        >
          <el-button plain icon="el-icon-caret-bottom" size="mini"
            >数据导入</el-button
          >
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="handleImport" icon="el-icon-upload"
              >数据导入</el-dropdown-item
            >
            <!-- <el-dropdown-item command="handleExport" icon="el-icon-download"
              >数据导出</el-dropdown-item
            > -->
          </el-dropdown-menu>
        </el-dropdown>
      </el-col>
      <el-col :span="2">
        <el-button plain icon="el-icon-plus" @click="handleAdd">新增</el-button>
      </el-col>
      <el-form
        style="align-items: center; margin: 0"
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        label-width="68px"
      >
        <el-form-item label="姓名" prop="userName">
          <el-input
            v-model="queryParams.userName"
            placeholder="请输入姓名"
            clearable
            size="mini"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
    </el-row>

    <el-table
      :height="tableHeight"
      v-loading="loading"
      :data="houseVillageList"
    >
      <el-table-column label="#" type="index" width="50" align="center">
        <template scope="scope">
          <span>{{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="姓名" align="center" prop="userName" />
      <el-table-column label="身份证号" align="center" prop="idCard" />
      <el-table-column label="项目组" align="center" prop="deptName" />
      <el-table-column label="职位" align="center" prop="application" />
      <el-table-column label="入职时间" align="center" prop="entryTime" />
      <el-table-column label="离职时间" align="center" prop="resignationTime" />
      <el-table-column label="出勤天数" align="center" prop="workDay" />
      <el-table-column label="迟到次数" align="center" prop="lateDay" />
      <el-table-column label="缺卡次数" align="center" prop="lackDay" />
      <el-table-column label="评分" align="center" prop="score" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:houseVillage:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:houseVillage:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改房源小区对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="700px"
      append-to-body
      close-on-click-modal
      v-dialogDrag
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="70px">
        <el-row :gutter="24" align="center" justify="center">
          <el-col :span="12">
            <el-form-item label="姓名" prop="userName">
              <el-input v-model="form.userName" placeholder="请输入姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="身份证" prop="idCard">
              <el-input v-model="form.idCard" placeholder="请输入身份证号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24" align="center" justify="center">
          <el-col :span="12">
            <el-form-item label="职位" prop="application">
              <el-select
                style="width: 100%"
                v-model="form.application"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in dict.type.application"
                  :key="item.value"
                  :label="item.label"
                  :value="item.label"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- <el-form-item label="薪资" prop="salary">
              <el-input v-model="form.salary" placeholder="请输入薪资" />
            </el-form-item> -->
            <el-form-item style="flex: 1" label="项目组" prop="deptName">
              <el-select
                style="width: 100%"
                v-model="form.deptName"
                placeholder="请选择"
                @change="changeDept"
              >
                <!-- 新增项目组 删除 -->
                <div
                  class="flex jca aic mt10 mb10"
                  style="color: #8492a6; font-size: 13px"
                >
                  <el-input
                    style="width: 60%"
                    v-model="deptName"
                    placeholder="请输入项目组名"
                  />
                  <div
                    style="
                      display: flex;
                      justify-content: center;
                      align-items: center;
                    "
                  >
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-plus"
                      @click="addDept"
                      >新增</el-button
                    >
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      @click="deleteDept"
                      >删除</el-button
                    >
                  </div>
                </div>
                <el-option
                  v-for="item in deptList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.label"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24" align="center" justify="center">
          <el-col :span="12"
            ><el-form-item label="入职" prop="entryTime">
              <el-date-picker
                style="width: 100%"
                v-model="form.entryTime"
                type="date"
                placeholder="选择日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12"
            ><el-form-item label="离职" prop="resignationTime">
              <el-date-picker
                style="width: 100%"
                v-model="form.resignationTime"
                type="date"
                placeholder="选择日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24" align="center" justify="center">
          <el-col :span="12"
            ><el-form-item label="出勤天数" prop="workDay">
              <el-input v-model="form.workDay" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="12"
            ><el-form-item label="迟到次数" prop="lateDay">
              <el-input v-model="form.lateDay" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24" align="center" justify="center">
          <el-col :span="12"
            ><el-form-item label="缺卡次数" prop="lackDay">
              <el-input v-model="form.lackDay" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="12"
            ><el-form-item label="评分" prop="score">
              <el-input v-model="form.score" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>

        <div class="flex jca aic mt10 mb10"></div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px">
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" slot="tip">
          <el-button
            type="primary"
            plain
            icon="el-icon-receiving"
            size="mini"
            @click="handleTemplete"
            style="float: right"
            >下载模板</el-button
          >
        </div>
        <div class="el-upload__tip" style="color: red" slot="tip">
          提示：仅允许导入“xls”或“xlsx”格式文件！
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
      <div class="exl"></div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listHouseVillage,
  getHouseVillage,
  delHouseVillage,
  addHouseVillage,
  updateHouseVillage,
} from "@/api/house/yggz";
import { deptlistapi, addDeptapi, deleteDeptapi } from "@/api/house/houseRules";

import { getToken } from "@/utils/auth";
import { Export } from "@/utils/Export";
import axios from "axios";
export default {
  name: "HouseVillage",
  dicts: ["application"],
  data() {
    return {
      baseURL: process.env.VUE_APP_BASE_API,
      // 导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/exstaff/upload",
      },
      value: true,
      // 取值方式
      options: [
        {
          value: "1",
          label: "导入",
        },
        {
          value: "2",
          label: "计算",
        },
      ],
      options1: [],

      options3: [],
      // 表格高度
      tableHeight: document.documentElement.clientHeight - 280,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 房源小区表格数据
      houseVillageList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
      },
      // 表单参数

      form: {
        userName: undefined,
        idCard: undefined,
        application: undefined,
        deptName: undefined,
        entryTime: undefined,
        resignationTime: undefined,
        lateDay: undefined,
        lackDay: undefined,
        workDay: undefined,
        score: undefined,
      },
      deptName: undefined,
      // 表单校验
      rules: {
        userName: [
          { required: true, message: "姓名不能为空", trigger: "blur" },
        ],
        idCard: [
          { required: true, message: "身份证号不能为空", trigger: "blur" },
        ],
        application: [
          { required: true, message: "职位不能为空", trigger: "change" },
        ],
        deptName: [
          { required: true, message: "项目组不能为空", trigger: "change" },
        ],
        entryTime: [
          { required: true, message: "入职时间不能为空", trigger: "change" },
        ],
      },
      // 项目组
      deptList: [],
    };
  },
  created() {
    this.getList();
    this.getDeptList();
  },
  methods: {
    /** 查询项目组列表 */
    getDeptList() {
      this.loading = true;
      deptlistapi().then((response) => {
        this.deptList = response.data.map((item) => {
          return { label: item.deptName, value: item.id };
        });
      });
    },

    // 项目组选中赋值
    changeDept() {
      this.deptName = this.form.deptName;
    },
    /** 新增项目组 */
    addDept(row) {
      this.$modal
        .confirm("是否确认新增？")
        .then(() => {
          return addDeptapi({ deptName: this.deptName });
        })
        .then(() => {
          this.getDeptList();
          this.getList();
          this.$modal.msgSuccess("新增成功");
          this.deptName = "";
          this.open = false;
        })
        .catch(() => {});
    },
    /** 删除项目组 */
    deleteDept() {
      this.$modal
        .confirm("是否确认删除？")
        .then(() => {
          return deleteDeptapi(this.deptName);
        })
        .then(() => {
          this.getDeptList();
          this.getList();
          this.$modal.msgSuccess("删除成功");
          this.deptName = "";
          this.open = false;
        })
        .catch(() => {});
    },
    /** 查询房源小区列表 */
    getList() {
      this.loading = true;
      listHouseVillage(this.queryParams).then((response) => {
        console.log(response);
        this.houseVillageList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        userName: undefined,
        idCard: undefined,
        application: undefined,
        salary: undefined,
        deptName: undefined,
        threeTerms: true,
        bonus: true,
        entryTime: undefined,
        resignationTime: undefined,
      };
      this.deptName = undefined;
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    // handleSelectionChange(selection) {
    //   this.ids = selection.map((item) => item.id);
    //   this.single = selection.length !== 1;
    //   this.multiple = !selection.length;
    // },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加员工";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      // this.reset();
      const id = row.id;
      getHouseVillage(id).then((response) => {
        this.form = response.data;
        this.form.delFlag = response.data.delFlag;

        this.open = true;
        this.title = "修改员工";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        // 将deptList转为字符串
        if (this.form.deptList) {
          this.form.deptList = this.form.deptList.join(",");
        }
        if (valid) {
          if (this.form.id != null) {
            updateHouseVillage(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addHouseVillage(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id;
      this.$modal
        .confirm("是否确认删除记录？")
        .then(function () {
          return delHouseVillage(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },

    /** 导入 */
    handleImport() {
      this.upload.title = "员工信息导入";
      this.upload.open = true;
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },

    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },

    // 下载模板
    handleTemplete() {
      axios({
        method: "post",
        headers: {
          Authorization: "Bearer " + getToken(),
        },
        url: this.baseURL + "/system/exstaff/export",

        responseType: "blob",
      })
        .then((res) => {
          const link = document.createElement("a");
          let blob = new Blob([res.data], { type: "application/x-excel" });
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          link.download = "员工信息模板.xlsx";
          document.querySelector(".exl").appendChild(link);
          link.click();
          document.querySelector(".exl").removeChild(link);
        })
        .catch((error) => {
          console.log(error);
          this.$modal.error({
            title: "错误",
            desc: "网络连接错误",
          });
        });
    },

    // 导出数据
    handleExport() {
      let data = {
        title: "员工信息数据",
        url: "/system/exstaff/exportList",
        token: getToken(),
        method: "get",
        data: this.queryParams,
      };
      Export(data);
    },

    // 导入操作触发
    handleImportCommand(command) {
      switch (command) {
        case "handleImport":
          this.handleImport();
          break;
        case "handleTemplete":
          this.handleTemplete();
          break;
        case "handleExport":
          this.handleExport();
          break;
        default:
          break;
      }
    },

    // 计数器
    handleChange(e) {
      this.form.sort = e;
    },
  },
};
</script>
