export const titleList = [
  { label: "时间", value: "time" },
  { label: "项目组", value: "group" },
  { label: "银行", value: "bankType" },
  { label: "一卡量", value: "yk" },
  { label: "有效卡", value: "yx" },
  { label: "单价", value: "yxPrice" },
  { label: "权益系数", value: "equities" },
  { label: "单价（权益）", value: "equitiesPrice" },
  { label: "企微绑卡率", value: "qw" },
  { label: "单价（企微）", value: "qwPrice" },
  { label: "动卡绑卡率", value: "dk" },
  { label: "单价（动卡）", value: "dkPrice" },
  { label: "快捷绑卡率", value: "kj" },
  { label: "单价（快捷）", value: "kjPrice" },
  { label: "M3活跃户", value: "m3" },
  { label: "M3竞赛奖励120元/活跃户", value: "m3Price" },
  { label: "M3活跃户对应系数", value: "m3Active" },
  { label: "M3活跃率", value: "m3Ratio" },
  { label: "M3活跃率对应系数", value: "m3Coefficient" },
  { label: "M3交易额<100", value: "m3A" },
  { label: "M3交易额<1000", value: "m3B" },
  { label: "M3交易额<3000", value: "m3C" },
  { label: "M3交易额<6000", value: "m3D" },
  { label: "M3交易额>=6000", value: "m3E" },
  { label: "交易额折算户数", value: "m3Num" },
  { label: "M3费用小计", value: "m3Sum" },
  { label: "品质扣罚", value: "punishment" },
  { label: "合计", value: "total" },
];
