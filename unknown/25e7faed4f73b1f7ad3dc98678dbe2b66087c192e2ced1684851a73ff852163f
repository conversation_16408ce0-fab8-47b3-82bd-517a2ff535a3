import axios from "axios";
import { MessageBox, Message } from "element-ui";

//{
//   title 标题
//   url 请求路径
//   token token
//   method 请求方式
//   data 携带参数
// }

// 通用导出方法封装
export function Export(data) {
  if (data.method == "get") {
    MessageBox.confirm(`确认导出${data.title}数据吗?`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(() => {
      // 导出请求
      axios({
        method: "get",
        params: data.data,
        headers: {
          Authorization: "Bearer " + data.token,
        },
        url: process.env.VUE_APP_BASE_API + data.url,
        responseType: "blob",
      })
        .then((res) => {
          const link = document.createElement("a");
          let blob = new Blob([res.data], { type: "application/x-excel" });
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          link.download = data.data.month
            ? data.data.month + `${data.title}.xlsx`
            : `${data.title}.xlsx`;
          document.querySelector(".exl").appendChild(link);
          link.click();
          document.querySelector(".exl").removeChild(link);
        })
        .catch((error) => {
          console.log(error);
          Message.error("网路连接失败，请稍后重试");
        });
    });
  } else {
    MessageBox.confirm(`确认导出${data.title}数据吗?`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(() => {
      // 导出请求
      axios({
        method: "post",
        data: data.data,
        headers: {
          Authorization: "Bearer " + data.token,
        },
        url: process.env.VUE_APP_BASE_API + data.url,
        responseType: "blob",
      })
        .then((res) => {
          const link = document.createElement("a");
          let blob = new Blob([res.data], { type: "application/x-excel" });
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          link.download = data.data.month
            ? data.data.month + `${data.title}.xlsx`
            : `${data.title}.xlsx`;
          document.querySelector(".exl").appendChild(link);
          link.click();
          document.querySelector(".exl").removeChild(link);
        })
        .catch((error) => {
          console.log(error);
          Message.error("网路连接失败，请稍后重试");
        });
    });
  }
}
