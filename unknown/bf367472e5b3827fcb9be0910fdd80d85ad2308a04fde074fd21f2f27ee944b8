<template>
  <div class="app-container">
    <el-card class="box-card">
      <div>
        <div style="font-weight: 600; font-size: 20px">
          {{ $route.query.field }}
        </div>
        <el-button
          style="margin: 20px 0 10px"
          type="primary"
          size="mini"
          @click="handleAdd"
          >添加规则</el-button
        >
      </div>
      <!-- 列表 -->
      <div class="flex jcb" style="flex-wrap: wrap">
        <el-empty
          style="margin: auto"
          v-if="list.length == 0"
          description="暂无数据"
          :image-size="200"
        ></el-empty>
        <el-card
          v-else
          class="box-card mt20"
          style="width: 49%"
          v-for="(item, index) in list"
          :key="item.id"
        >
          <!--头部 -->
          <div slot="header" class="title">
            <div class="flex jcb aic" style="width: 200px">
              <div>{{ item.name }}</div>
              <div>类型：{{ item.typeName }}</div>
            </div>
            <div>
              <el-button type="text" @click="handleUpdate(item)"
                >修改</el-button
              >
              <el-button type="text" @click="handleDelete(item)"
                >删除</el-button
              >
            </div>
          </div>
          <!--规则 -->
          <div style="font-size: 14px; color: #8a8a8a">
            <!-- 计算 -->
            <div v-if="item.type == 1" class="mt10">
              {{ item.typeName }}：<span
                style="font-weight: 600; color: #515a6e"
                >{{ item.formula }}</span
              >
            </div>
            <!-- 范围 -->
            <div v-else-if="item.type == 2" class="mt10">
              <div class="flex jcc">
                <div>{{ item.typeName }}：</div>
                <el-table :data="item.scopeRule" size="mini" :border="true">
                  <el-table-column align="center" prop="mini" label="最小值">
                  </el-table-column>
                  <el-table-column align="center" prop="max" label="最大值">
                  </el-table-column>
                  <el-table-column
                    align="center"
                    prop="commission"
                    label="佣金/件"
                  >
                  </el-table-column>
                </el-table>
              </div>
            </div>
            <!-- 累计 -->
            <div v-else-if="item.type == 3" class="mt10">
              <div class="flex jcc">
                <div>{{ item.typeName }}：</div>
                <el-table :data="item.scopeRule" size="mini" :border="true">
                  <el-table-column align="center" prop="mini" label="最小值">
                  </el-table-column>
                  <el-table-column align="center" prop="max" label="最大值">
                  </el-table-column>
                  <el-table-column
                    align="center"
                    prop="cumulative"
                    label="累加取值"
                  >
                  </el-table-column>
                  <el-table-column
                    align="center"
                    prop="commission"
                    label="佣金/件"
                  >
                  </el-table-column>
                </el-table>
              </div>
            </div>
            <div class="mt10">
              适用职位：{{ item.application ? item.application : "无" }}
            </div>
            <div class="mt10">
              适用项目：{{
                item.project == [] ? "无" : item.project.join("、")
              }}
            </div>

            <!-- <div class="mt10">
              规则备注：{{ item.remark ? item.remark : "无" }}
            </div> -->
          </div>
        </el-card>
      </div>

      <!--  -->
      <!-- 添加或修改房源小区对话框 -->
      <el-dialog
        :title="title"
        :visible.sync="open"
        width="700px"
        append-to-body
        close-on-click-modal
        v-dialogDrag
      >
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="规则名" prop="name">
                <el-input v-model="form.name" placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="规则类型" prop="type">
                <el-select
                  v-model="form.type"
                  placeholder="请选择"
                  @change="onChange"
                >
                  <el-option
                    v-for="item in options2"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="适用职位" prop="application">
                <el-select v-model="form.application" placeholder="请选择">
                  <el-option
                    v-for="item in dict.type.application"
                    :key="item.value"
                    :label="item.label"
                    :value="item.label"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <!-- 范围规则关联字段 -->
              <el-form-item
                v-if="form.type == 2 || form.type == 3"
                label="关联字段"
                prop="reFieldId"
              >
                <el-select v-model="form.reFieldId" placeholder="请选择">
                  <el-option
                    v-for="item in options3"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <!--  -->

          <el-transfer
            style="display: flex; justify-content: center; align-items: center"
            :titles="['未选择项目组', '已选择项目组']"
            v-model="form.deptList"
            :data="arr1"
          ></el-transfer>

          <!--  -->
          <!-- 计算规则 -->
          <div v-if="form.type == 1">
            <el-row :gutter="24" class="mt10">
              <el-col :span="24">
                <el-form-item label="计算规则" prop="formula">
                  <el-input
                    type="textarea"
                    v-model="form.formula"
                    placeholder="点击下方按钮输入内容"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <div style="display: flex">
              <div style="">
                <el-button
                  v-for="(item, index) in 10"
                  :key="item"
                  plain
                  size="mini"
                  @click="Calculate(index)"
                  style="margin-left: 10px; margin-bottom: 5px"
                >
                  {{ index }}
                </el-button>
                <el-button
                  plain
                  size="mini"
                  @click="Calculate('.')"
                  style="margin-left: 10px; margin-bottom: 5px"
                >
                  .
                </el-button>
                <div v-for="(items, index) in HeaderList" :key="index">
                  <div
                    style="font-weight: 600; padding-left: 15px"
                    class="mt10 mb10"
                  >
                    {{ items.title }}:
                  </div>
                  <el-button
                    v-for="row in items.list"
                    :key="row.value"
                    :type="
                      row.type == 1
                        ? 'success'
                        : row.type == 2
                        ? 'primary'
                        : row.type == 3
                        ? 'info'
                        : ''
                    "
                    plain
                    size="mini"
                    @click="Calculate(row.label)"
                    style="margin-left: 10px; margin-bottom: 5px"
                  >
                    {{ row.label }}
                  </el-button>
                </div>
              </div>

              <div
                style="
                  width: 30%;
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  align-items: center;
                "
              >
                <div
                  style="
                    display: flex;
                    width: 100px;
                    justify-content: space-around;
                    margin-bottom: 5px;
                  "
                >
                  <el-button @click="Calculate('+')" style="margin-left: 0">
                    +
                  </el-button>
                  <el-button @click="Calculate('-')" style="margin-left: 0">
                    -
                  </el-button>
                </div>
                <div
                  style="
                    display: flex;
                    width: 100px;
                    justify-content: space-around;
                    margin-bottom: 5px;
                  "
                >
                  <el-button @click="Calculate('*')" style="margin-left: 0">
                    *
                  </el-button>
                  <el-button @click="Calculate('/')" style="margin-left: 0">
                    /
                  </el-button>
                </div>
                <div
                  style="
                    display: flex;
                    width: 100px;
                    justify-content: space-around;
                    margin-bottom: 5px;
                  "
                >
                  <el-button @click="Calculate('(')" style="margin-left: 0">
                    (
                  </el-button>
                  <el-button @click="Calculate(')')" style="margin-left: 0">
                    )
                  </el-button>
                </div>

                <div
                  style="
                    display: flex;
                    width: 100px;
                    justify-content: space-around;
                    margin-bottom: 5px;
                  "
                >
                  <el-button
                    @click="Calculate('回删')"
                    style="margin-left: 0; width: 90px"
                    type="info"
                  >
                    回删
                  </el-button>
                </div>
                <div
                  style="
                    display: flex;
                    width: 100px;
                    justify-content: space-around;
                    margin-bottom: 5px;
                  "
                >
                  <el-button
                    @click="Calculate('清空')"
                    style="margin-left: 0; width: 90px"
                    type="danger"
                  >
                    清空
                  </el-button>
                </div>
              </div>
            </div>
          </div>
          <!-- 范围规则 -->
          <div v-if="form.type == 2">
            <el-button
              style="transform: translateY(63px)"
              size="mini"
              type="primary"
              @click="addDomain"
              >新增范围</el-button
            >
            <el-form-item
              v-for="(domain, index) in form.scopeRule"
              :label="'范围规则' + index"
              :key="domain.key"
              :prop="'scopeRule.' + index + '.mini'"
              :rules="{
                required: true,
                message: '范围规则不能为空',
                trigger: 'blur',
              }"
            >
              <el-row :gutter="24" style="transform: translateX(-12px)">
                <el-col :span="7">
                  最小值
                  <el-input type="number" v-model="domain.mini"></el-input>
                </el-col>
                <el-col :span="7">
                  最大值
                  <el-input type="number" v-model="domain.max"></el-input>
                </el-col>
                <el-col :span="7">
                  佣金/件
                  <el-input
                    type="number"
                    v-model="domain.commission"
                  ></el-input>
                </el-col>
                <el-button
                  type="danger"
                  icon="el-icon-delete"
                  circle
                  style="transform: translateY(31px)"
                  @click.prevent="removeDomain(domain)"
                ></el-button>
              </el-row>
            </el-form-item>
          </div>

          <!-- 累加规则 -->
          <div v-if="form.type == 3">
            <el-button
              style="transform: translateY(63px)"
              size="mini"
              type="primary"
              @click="addDomain"
              >新增范围</el-button
            >
            <el-form-item
              v-for="(domain, index) in form.scopeRule"
              :label="'累加规则' + index"
              :key="domain.key"
              :prop="'scopeRule.' + index + '.mini'"
              :rules="{
                required: true,
                message: '累加规则不能为空',
                trigger: 'blur',
              }"
            >
              <el-row :gutter="24" style="transform: translateX(-12px)">
                <el-col :span="5">
                  最小值
                  <el-input type="number" v-model="domain.mini"></el-input>
                </el-col>
                <el-col :span="5">
                  最大值
                  <el-input type="number" v-model="domain.max"></el-input>
                </el-col>
                <el-col :span="5">
                  累加取值
                  <el-input
                    type="number"
                    v-model="domain.commission"
                  ></el-input>
                </el-col>
                <el-col :span="5">
                  佣金值
                  <el-input
                    type="number"
                    v-model="domain.cumulative"
                  ></el-input>
                </el-col>
                <el-button
                  type="danger"
                  icon="el-icon-delete"
                  circle
                  style="transform: translateY(31px)"
                  @click.prevent="removeDomain(domain)"
                ></el-button>
              </el-row>
            </el-form-item>
          </div>
        </el-form>

        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import {
  listHouseVillage,
  getHouseVillage,
  delHouseVillage,
  addHouseVillage,
  updateHouseVillage,
  deptList,
  MeterHeadList,
  updateHouseVillages,
  getList,
  tablecalcAll,
  deptlistapi,
} from "@/api/house/houseRules";

import { listHeaders } from "@/api/house/houseVillage";
import draggable from "vuedraggable";
export default {
  name: "Salaryrules",
  dicts: ["application"],
  components: {
    draggable,
  },
  data() {
    return {
      // 部门

      arr1: [],
      arr2: [],
      value: [],
      list: [],
      // 动态表单
      form: {
        name: "",
        rulesList: [],
        deptList: [],
        fieldId: "",
        type: "",
        reFieldId: "",
        formula: "",
        application: "",
        scopeRule: [
          {
            mini: "",
            max: "",
            commission: "",
          },
        ],
      },

      // 表单校验
      rules: {
        name: [{ required: true, message: "规则名不能为空", trigger: "blur" }],
        deptList: [
          { required: true, message: "部门不能为空", trigger: "blur" },
        ],
        reFieldId: [
          { required: true, message: "关联字段不能为空", trigger: "blur" },
        ],
        application: [
          { required: true, message: "应用范围不能为空", trigger: "blur" },
        ],
        formula: [
          { required: true, message: "计算规则不能为空", trigger: "blur" },
        ],
        type: [{ required: true, message: "请选择规则", trigger: "blur" }],
      },
      open: false,
      title: "新增规则",
      // 取值方式
      options2: [
        {
          value: 1,
          label: "计算规则",
        },
        {
          value: 2,
          label: "范围规则",
        },
        {
          value: 3,
          label: "累计规则",
        },
      ],
      options3: [],
      // 表头列表
      HeaderList: [],
    };
  },
  activated() {
    this.getMeterHeadAllList();
    this.getDeptList();
    this.getMeterHeadList();
    this.getList();
  },

  methods: {
    //& ===================================
    onStart() {},
    onEnd() {
      this.form.deptList = this.arr1.map((item) => item.id);
    },

    //& ===================================

    // 获取规则列表
    async getList() {
      let res = await getList(this.$route.query.id);
      this.list = res.data;
    },

    /** 关联字段列表 */
    getMeterHeadList() {
      this.loading = true;
      MeterHeadList(1).then((response) => {
        this.options3 = response.data
          .filter((item) => {
            // 过滤出中括号内包含'中信' 或者 '广发/浦发' 的数据
            const match = item.field.match(/\[(.*?)\]/);
            return (
              match &&
              (match[1] === "中信" ||
                ["广发", "浦发", "管理层绩效"].some((bank) =>
                  match[1].includes(bank)
                ))
            );
          })
          .map((item) => {
            // 保留原始的 field 字段作为 label
            return { label: item.field, value: item.id };
          });
      });
    },
    /** 查询所有表头 计算器列表 */
    getMeterHeadAllList() {
      this.loading = true;
      tablecalcAll("").then((response) => {
        const categorized = {};

        response.data.forEach((item) => {
          const titleMatch = item.field.match(/\[(.*?)\]/);
          if (titleMatch) {
            const title = titleMatch[1];
            const label = item.field.replace(/\[.*?\]/, "").trim(); // 去掉中括号及其内容

            if (!categorized[title]) {
              categorized[title] = { title, list: [] };
            }

            categorized[title].list.push({
              label,
              value: item.id,
              type: item.type,
            });
          }
        });

        this.HeaderList = Object.values(categorized);
        console.log(this.HeaderList);
        // this.HeaderList = response.data.map((item) => {
        //   return { label: item.field, value: item.id, type: item.type };
        // });
      });
    },
    /** 查询项目组列表 */
    getDeptList() {
      this.loading = true;
      deptlistapi().then((response) => {
        this.arr1 = response.data.map((item) => {
          return { label: item.deptName, key: item.id };
        });
      });
    },

    //计算器
    Calculate(e) {
      if (e == "清空") {
        this.form.rulesList = [];
      } else if (e == "回删") {
        this.form.rulesList = this.form.rulesList.slice(0, -1);
      } else {
        this.form.rulesList.push(e);
      }
      this.form.formula = this.form.rulesList.join("");
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        rulesList: [],
        name: null,
        deptList: [],
        fieldId: null,
        type: null,
        reFieldId: null,
        formula: null,
        application: null,
        scopeRule: [
          {
            mini: "",
            max: "",
            commission: "",
          },
        ],
      };
      this.form.fieldId = this.$route.query.id;
      this.resetForm("form");
    },
    // 改变规则时 清空规则表单
    onChange() {
      this.form.reFieldId = "";
      this.form.formula = "";
      this.form.scopeRule = [
        {
          mini: "",
          max: "",
          commission: "",
        },
      ];
    },
    // 删除一项
    removeDomain(item) {
      var index = this.form.scopeRule.indexOf(item);
      if (this.form.scopeRule.length == 1) {
        return this.$modal.msgError("至少保留一项");
      } else {
        if (index !== -1) {
          this.form.scopeRule.splice(index, 1);
        }
      }
    },
    // 添加一项
    addDomain() {
      this.form.scopeRule.push({
        mini: "",
        max: "",
        commission: "",
        key: Date.now(),
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      getHouseVillage(row.id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改规则";
      });
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增规则";
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateHouseVillage(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.getList();
              this.open = false;
            });
          } else {
            addHouseVillage(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.getList();
              this.open = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm("是否确认删除规则？")
        .then(function () {
          return delHouseVillage(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
  },
};
</script>
<style scoped>
.title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}
.flex {
  display: flex;
}
.jcc {
  justify-content: center;
}
.jca {
  justify-content: space-around;
}
.jce {
  justify-content: space-evenly;
}
.jcb {
  justify-content: space-between;
}
.aic {
  align-items: center;
}
.mt10 {
  margin-top: 10px;
}
.mb10 {
  margin-bottom: 10px;
}

/*定义要拖拽元素的样式*/
.ghostClass {
  background-color: blue !important;
}

.chosenClass {
  background-color: rgb(214, 214, 214) !important;
  opacity: 1 !important;
}

.dragClass {
  background-color: rgb(214, 214, 214) !important;
  opacity: 1 !important;
  box-shadow: none !important;
  outline: none !important;
  background-image: none !important;
}

.box {
  margin: 10px;
  width: 100%;
  display: flex;
  padding: 0 10px;
  justify-content: space-around;
}

.title {
  padding: 6px 12px;
}

.col {
  width: 45% !important;
  padding: 10px 0;
  border: solid 1px #eee;
  border-radius: 5px;
  overflow-x: hidden;
  overflow-y: auto;
  margin-bottom: 10px;
}

.item {
  padding: 10px 12px;
  margin: 0px 10px 0px 10px;
  border: solid 1px #eee;
  background-color: #ececec;
  border-radius: 5px;
}

.item:hover {
  background-color: rgb(249, 249, 249);
  cursor: move;
}

.item + .item {
  border-top: none;
  margin-top: 6px;
}
.min-h200 {
  height: 200px;
  display: block;
}
</style>
