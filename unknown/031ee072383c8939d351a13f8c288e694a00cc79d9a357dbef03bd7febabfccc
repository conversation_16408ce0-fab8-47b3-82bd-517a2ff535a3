<template>
  <div class="app-container">
    <el-card class="box-card1">
      <el-row :gutter="24" align="center" justify="center">
        <el-col :span="8" class="boxtitle">
          <div style="font-size: 14px; color: #909090;">时间</div>
          <div style="font-size: 22px;">{{ $route.query.time }}</div>
        </el-col>
        <el-col :span="8" class="boxtitle">
          <div style="font-size: 14px; color: #909090;">支出 (元)</div>
          <div style="font-size: 22px;">{{ sum.price }}</div>
        </el-col>
        <el-col :span="8" class="boxtitle boxtitle3">
          <div style="font-size: 14px; color: #909090;">收入 (元)</div>
          <div style="font-size: 22px;">{{ sum.income }}</div>
        </el-col>
      </el-row>
    </el-card>



    <el-card class="box-card2">
      <el-row :gutter="24" align="center" justify="center" style="height: 40px;">
        <el-col :span="1.5">
          <el-button plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-dropdown size="medium" @command="(command) => handleImportCommand(command)">
            <el-button plain icon="el-icon-caret-bottom" size="mini">数据导入</el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="handleImport" icon="el-icon-upload">数据导入</el-dropdown-item>
              <el-dropdown-item command="handleExport" icon="el-icon-download">数据导出</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <div class="exl"></div>

        </el-col>
        <!-- <el-col :span="11">
        </el-col> -->
        <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px" class="el-form-search">
          <el-form-item label="摘要" prop="type">
            <el-select v-model="queryParams.type" placeholder="请选择摘要">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="名称" prop="field" class="el-form-search-item">
            <el-input v-model="queryParams.field" placeholder="请输入名称" clearable size="mini"
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="时间" prop="upField" class="el-form-search-item">
            <el-date-picker v-model="queryParams.time" type="year" placeholder="选择年">
            </el-date-picker>
          </el-form-item> -->

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-row>



      <el-table v-loading="loading" :data="houseVillageList">
        <el-table-column label="#" type="index" width="50" align="center">
          <template scope="scope">
            <span>{{
        (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
      }}</span>
          </template>
        </el-table-column>
        <el-table-column label="时间" align="center" prop="detailTime" />

        <el-table-column label="部门" align="center" prop="name" />
        <el-table-column label="摘要" align="center" prop="type" />
        <el-table-column label="收入" align="center" prop="income" />
        <el-table-column label="支出" align="center" prop="price" />
        <el-table-column label="经办人" align="center" prop="nickName" />
        <el-table-column label="备注" width="300" align="center" prop="remark" />
        <el-table-column label="添加时间" align="center" prop="createTime" />
        <el-table-column label="添加账户" align="center" prop="createBy" />

        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">

          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>

        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />

      <!-- 添加或修改房源小区对话框 -->
      <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body close-on-click-modal v-dialogDrag>
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="日期" prop="detailTime">
                <el-date-picker style="max-width: 140px;" value-format="yyyy-MM-dd" v-model="form.detailTime"
                  type="date" placeholder="选择日期">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="摘要" prop="type">
                <el-select v-model="form.type" placeholder="请选择摘要">
                  <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="部门" prop="name">
                <el-input v-model="form.name" placeholder="请输入部门" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="收入" prop="income">
                <el-input v-model="form.income" placeholder="请输入费用" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="支出" prop="price">
                <el-input v-model="form.price" placeholder="请输入费用" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="经办人" prop="nickName">
                <el-input v-model="form.nickName" placeholder="请输入经办人" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input type="textarea" :show-word-limit="true" :maxlength="100"
                  :autosize="{ minRows: 2, maxRows: 4 }" placeholder="请输入备注" v-model="form.remark">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>




      <!-- 导入对话框 -->
      <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px">
        <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" :action="upload.url"
          :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess"
          :auto-upload="false" drag>
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">
            将文件拖到此处，或
            <em>点击上传</em>
          </div>
          <div class="el-upload__tip" slot="tip">
            <el-button type="primary" plain icon="el-icon-receiving" size="mini" @click="handleTemplete"
              style="float: right">下载模板</el-button>
          </div>
          <div class="el-upload__tip" style="color: red" slot="tip">
            提示：仅允许导入“xls”或“xlsx”格式文件！
          </div>
        </el-upload>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
        <div class="exl"></div>
      </el-dialog>











    </el-card>
  </div>
</template>

<script>

import {
  listHouseVillage,
  getHouseVillage,
  delHouseVillage,
  addHouseVillage,
  updateHouseVillage,
  summarylist,
  sumlist
} from "@/api/house/Journal";
import axios from "axios";
import { Export } from "@/utils/Export";
import { getToken } from "@/utils/auth";
export default {
  name: "Journal",
  dicts: ["sys_common_status"],
  data() {
    return {
      baseURL: process.env.VUE_APP_BASE_API,
      // 导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/expend/upload",
      },
      value: true,
      // 取值方式
      options: [

      ],


      // 表格高度
      tableHeight: document.documentElement.clientHeight - 280,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 房源小区表格数据
      houseVillageList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,

      },
      // 表单参数

      form: {

      },
      sum: {},
      // 表单校验
      rules: {
        detailTime: [{ required: true, message: "时间不能为空", trigger: "blur" }],
        type: [{ required: true, message: "摘要不能为空", trigger: "change" }],
        name: [{ required: true, message: "部门不能为空", trigger: "blur" }],
        price: [{ required: true, message: "支出不能为空", trigger: "blur" }],
        nickName: [{ required: true, message: "经办人不能为空", trigger: "blur" }],
      },
    };
  },
  activated() {
    this.queryParams.time = this.$route.query.time
    this.upload.url = process.env.VUE_APP_BASE_API + "/system/expend/upload?time=" + this.queryParams.time
    this.getsummarylist()
    this.getsum()
    this.getList();
  },

  methods: {
    getsum() {
      sumlist(this.queryParams).then((response) => {
        console.log(response);
        this.sum = response.data
      });
    },



    // 摘要列表
    getsummarylist() {
      summarylist().then((response) => {

        this.options = response.data.map((item) => {
          return {
            value: item.dictValue,
            label: item.dictLabel,
          };
        });
      });
    },





    /** 查询房源小区列表 */
    getList() {
      this.loading = true;
      listHouseVillage(this.queryParams).then((response) => {

        this.houseVillageList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {

      this.form = {


      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    // handleSelectionChange(selection) {
    //   this.ids = selection.map((item) => item.id);
    //   this.single = selection.length !== 1;
    //   this.multiple = !selection.length;
    // },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id
      getHouseVillage(id).then((response) => {
        console.log(response);
        this.form = response.data;
        this.num = response.data.sort;
        // 字符串切成数组
        this.form.deptList = this.form.deptList ? response.data.deptList.split(",") : ''

        this.open = true;
        this.title = "修改";
      });
    },
    /** 提交按钮 */
    submitForm() {


      this.$refs["form"].validate((valid) => {
        // 将deptList转为字符串
        if (this.form.deptList) {
          this.form.deptList = this.form.deptList.join(",");
        }
        if (valid) {
          if (this.form.id != null) {
            updateHouseVillage(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addHouseVillage(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id;
      this.$modal
        .confirm("是否确认删除记录？")
        .then(function () {
          return delHouseVillage(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导入 */
    handleImport() {
      this.upload.title = "导入";
      this.upload.open = true;
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },

    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },


    // 下载模板
    handleTemplete() {
      axios({
        method: 'post',
        headers: {
          'Authorization': 'Bearer ' + getToken()
        },
        url: this.baseURL + '/system/expend/exportHead?time=' + this.queryParams.time,

        responseType: 'blob'
      }).then((res) => {
        const link = document.createElement('a');
        let blob = new Blob([res.data], { type: 'application/x-excel' });
        link.style.display = 'none';
        link.href = URL.createObjectURL(blob);
        link.download = '模板.xlsx';
        document.querySelector('.exl').appendChild(link);
        link.click();
        document.querySelector('.exl').removeChild(link);
      }).catch(error => {
        console.log(error);
        this.$modal.error({
          title: '错误',
          desc: '网络连接错误'
        });
      });
    },


    // 导出数据
    handleExport() {
      let data = {
        title: '数据',
        url: '/system/expend/exportList?time=' + this.queryParams.time,
        token: getToken(),
        method: 'get',
        data: this.queryParams
      }
      Export(data)
      // this.$confirm("确认导出三绑奖罚数据吗?", "提示", {
      //   confirmButtonText: "确定",
      //   cancelButtonText: "取消",
      //   type: "warning",
      // }).then(() => {
      //   // 导出请求
      //   axios({
      //     method: 'get',
      //     params: this.queryParams,
      //     headers: {
      //       'Authorization': 'Bearer ' + getToken()
      //     },
      //     url: this.baseURL + '/system/expend/exportList',
      //     responseType: 'blob'
      //   }).then((res) => {
      //     const link = document.createElement('a');
      //     let blob = new Blob([res.data], { type: 'application/x-excel' });
      //     link.style.display = 'none';
      //     link.href = URL.createObjectURL(blob);
      //     link.download = this.queryParams.month ? this.queryParams.month + '三绑奖罚.xlsx' : '三绑奖罚.xlsx';
      //     document.querySelector('.exl').appendChild(link);
      //     link.click();
      //     document.querySelector('.exl').removeChild(link);
      //   }).catch(error => {
      //     console.log(error);
      //     this.$modal.error({
      //       title: '错误',
      //       desc: '网络连接错误'
      //     });
      //   });




      // });
    },




    // 导入操作触发
    handleImportCommand(command) {
      switch (command) {
        case "handleImport":
          this.handleImport();
          break;
        case "handleTemplete":
          this.handleTemplete();
          break;
        case "handleExport":
          this.handleExport();
          break;
        default:
          break;
      }
    },

    // 重新绘制表格，防止抖动与滚动条异常
    setDataTable() {
      this.$nextTick(() => {
        this.$refs.dataTable.doLayout();
      });
    },


  },
};
</script>
<style>
.box-card1 {
  margin-top: 40px;
}

.box-card2 {
  margin-top: 40px;
}

.boxtitle {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
  height: 80px;
  border-right: 1px solid #e1e1e1;
}

.boxtitle3 {
  border-right: 0;
}
</style>