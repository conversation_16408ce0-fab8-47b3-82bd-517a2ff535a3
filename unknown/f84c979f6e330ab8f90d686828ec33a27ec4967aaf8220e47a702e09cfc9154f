<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb5">
      <el-col :span="1.5">
        <!-- <el-button
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:houseRoom:add']"
          >新增</el-button
        > -->
      </el-col>
      <el-col :span="1.5">
        <!-- <el-button
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:houseRoom:edit']"
          >修改</el-button
        > -->
      </el-col>
      <el-col :span="1.5">
        <!-- <el-button
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:houseRoom:remove']"
          >删除</el-button
        > -->
      </el-col>
      <el-col :span="1.5">
        <el-dropdown
          size="medium"
          @command="(command) => handleImportCommand(command)"
        >
          <el-button plain icon="el-icon-caret-bottom" size="mini"
            >数据导入</el-button
          >
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="handleImport" icon="el-icon-upload"
              >数据导入</el-dropdown-item
            >
            <!-- <el-dropdown-item command="handleExport" icon="el-icon-download">数据导出</el-dropdown-item> -->
          </el-dropdown-menu>
        </el-dropdown>
        <div class="exl"></div>
      </el-col>
      <el-col :span="1.5">
        <!-- <el-button plain icon="el-icon-s-promotion" :disabled="houseRoomList.length == 0" size="mini"
          @click="dialogVisible = !dialogVisible">发布</el-button> -->
      </el-col>
      <!-- 发布弹窗 -->
      <!-- <el-dialog title="请选择项目组进行发布" :visible.sync="dialogVisible" width="30%" @close="deptIdList = []">
        <el-select v-model="deptIdList" multiple placeholder="请选择项目组">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="publish">确 定</el-button>
        </span>
      </el-dialog> -->

      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        label-width="60px"
        class="el-form-search"
      >
        <!-- <el-form-item label="姓名" prop="nickName" class="el-form-search-item">
          <el-input v-model="queryParams.nickName" placeholder="请输入姓名"></el-input>
        </el-form-item> -->
        <!-- <el-form-item label="手机号" prop="phone" class="el-form-search-item">
          <el-input v-model="queryParams.phone" placeholder="请输入手机号"></el-input>
        </el-form-item> -->
        <el-form-item label="银行" prop="deptId" class="el-form-search-item">
          <el-select v-model="queryParams.bankType" placeholder="请选择银行">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="月份" prop="month" class="el-form-search-item">
          <el-date-picker
            :clearable="false"
            v-model="queryParams.month"
            format="yyyy 年 MM 月"
            value-format="yyyy-MM"
            type="month"
            placeholder="请选择月份"
          >
          </el-date-picker>
        </el-form-item>

        <el-form-item class="el-form-search-item">
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
    </el-row>

    <el-table
      :height="tableHeight"
      v-loading="loading"
      :data="houseRoomList"
      ref="dataTable"
    >
      <el-table-column label="#" type="index" width="50" align="center">
        <template scope="scope">
          <span>{{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}</span>
        </template>
      </el-table-column>

      <el-table-column
        v-for="(item, index) in titleList"
        :key="item.value"
        :label="item.label"
        align="center"
        :prop="item.value"
      >
        <template slot-scope="{ row }">
          {{
            item.label === "银行"
              ? row[item.value] === 1
                ? "中信"
                : row[item.value] === 2
                ? "广发"
                : row[item.value] === 3
                ? "浦发"
                : ""
              : row[item.value]
          }}
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getVillageList"
    />

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px">
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" slot="tip">
          <el-button
            type="primary"
            plain
            icon="el-icon-receiving"
            size="mini"
            @click="handleTemplete"
            style="float: right"
            >下载模板</el-button
          >
        </div>
        <div class="el-upload__tip" style="color: red" slot="tip">
          提示：仅允许导入“xls”或“xlsx”格式文件！
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
      <div class="exl"></div>
    </el-dialog>
  </div>
</template>

<script>
import { getListApi } from "@/api/revenue/project";
import { titleList } from "./titleList.js";
import { getToken } from "@/utils/auth";
import { Export } from "@/utils/Export";
import axios from "axios";
export default {
  name: "ThreeBindings",
  dicts: ["sys_notice_status"],
  // components: { evaluate, selectUser },
  data() {
    return {
      titleList,
      baseURL: process.env.VUE_APP_BASE_API,
      // 导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/accountBank/upload",
      },
      // 表格高度
      tableHeight: document.documentElement.clientHeight - 280,
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 房源详情表格数据
      houseRoomList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
      },
      // 表头列表
      headerList: [],
      // 项目组列表
      options: [
        {
          label: "中信",
          value: "1",
        },
        {
          label: "广发",
          value: "2",
        },
        {
          label: "浦发",
          value: "3",
        },
      ],
      // 发布弹出层
      dialogVisible: false,
      // 发布项目组数组
      deptIdList: [],
    };
  },
  created() {
    // this.getDeptList()
    // this.getHeaders()
    this.getVillageList();
  },
  methods: {
    // 发布表单关闭
    // handleClose() {
    //   this.deptIdList = []
    // },
    // 发布按钮
    // async publish() {
    //   let res = await releaseForm({ deptIdList: this.deptIdList })

    //   if (res.code == 200) {
    //     this.$modal.msgSuccess("发布成功！");
    //     this.dialogVisible = false
    //     this.getHeaders()
    //     this.getVillageList()
    //   }
    // },
    // 查询表头列表
    // getHeaders() {
    //   this.loading = true;
    //   getHeaderList(this.queryParams).then((response) => {
    //     if (response.data.length == 0) {
    //       this.$modal.msgError("没有该月工资条！");

    //     } else {
    //       let yourArray
    //       yourArray = response.data.map(item => {

    //         if (item.field.split(']')[1] == '') {
    //           return {
    //             ...item,
    //             field: item.field.replace(/^\[|\]$/g, '')
    //           }
    //         } else {
    //           return item
    //         }
    //       })
    //       // 遍历数组，处理每个对象的 field 字段
    //       yourArray.forEach(function (obj, index) {
    //         // 使用正则表达式匹配方括号中的内容
    //         var match = obj.field.match(/\[(.*?)\](.*)/);
    //         var timestamp = new Date().getTime();
    //         if (match && match.length >= 3) {
    //           // 如果找到匹配项，拆分为母项和子项
    //           obj.field = match[1];  // 母项
    //           obj.child = Object.assign({}, obj);  // 克隆母项属性作为子项
    //           obj.child.field = match[2];  // 子项的 field 为方括号后面的内容
    //           obj.child.id = obj.id;  // 子项的 field 为方括号后面的内容
    //           obj.id = timestamp + index;  // 子项的 field 为方括号后面的内容

    //         }
    //       });

    //       // 遍历数组，处理每个对象的 child 字段
    //       yourArray.forEach(function (obj) {
    //         // 确保 child 是一个数组
    //         if (obj.child && !Array.isArray(obj.child)) {
    //           obj.child = [obj.child];
    //         }
    //       });

    //       // 一边筛不干净 筛两遍
    //       for (let index = 0; index < 4; index++) {
    //         // 遍历数组，处理每个对象的 child 字段
    //         yourArray.forEach(function (obj, index, array) {
    //           // 如果有 child 属性
    //           if (obj.child && obj.child.length > 0) {
    //             // 查找是否有相同 field 的对象
    //             var matchingIndex = array.findIndex(item => item.field === obj.field && item !== obj);

    //             if (matchingIndex !== -1) {
    //               // 合并相同 field 的对象的 child 属性
    //               array[matchingIndex].child.push(...obj.child);
    //               // 删除当前对象
    //               array.splice(index, 1);
    //             }
    //           }
    //         });

    //       }

    //       // 遍历数组，处理每个对象的 child 字段
    //       yourArray.forEach(function (obj) {
    //         // 如果有 child 属性 给它排序
    //         if (obj.child && obj.child.length > 0) {
    //           obj.child.sort((a, b) => a.id - b.id);
    //         }

    //       });

    //       this.headerList = yourArray
    //     }
    //     this.loading = false;
    //   });
    // },
    /** 查询表格列表列表 */
    getVillageList() {
      this.loading = true;
      getListApi(this.queryParams).then((response) => {
        this.total = response.total;
        this.houseRoomList = response.rows.map((item) => {
          return {
            ...item,
            // 保留两位小数
            dkRatio: (item.dkRatio * 100).toFixed(2) + "%",
            kjRatio: (item.kjRatio * 100).toFixed(2) + "%",
            wxRatio: (item.wxRatio * 100).toFixed(2) + "%",
          };
        });

        this.loading = false;
      });
    },
    /** 查询项目组列表 */
    // getDeptList() {
    //   this.loading = true;
    //   deptList().then((response) => {

    //     this.options = response.data.map(item => {
    //       return { label: item.deptName, value: item.deptId };
    //     })

    //   });
    // },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getVillageList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 导入 */
    handleImport() {
      this.upload.title = "导入";
      this.upload.url =
        process.env.VUE_APP_BASE_API +
        "/system/accountBank/upload?month=" +
        this.queryParams.month +
        "&bankType=" +
        this.queryParams.bankType;

      if (this.queryParams.month && this.queryParams.bankType) {
        this.upload.open = true;
      } else {
        this.$modal.msgError("请选择银行和月份");
      }
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.url =
        process.env.VUE_APP_BASE_API + "/system/accountBank/upload";
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },

    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },

    // 下载模板
    handleTemplete() {
      axios({
        method: "post",
        headers: {
          Authorization: "Bearer " + getToken(),
        },
        url: this.baseURL + "/system/accountBank/exportHead",

        responseType: "blob",
      })
        .then((res) => {
          const link = document.createElement("a");
          let blob = new Blob([res.data], { type: "application/x-excel" });
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          link.download = "模板.xlsx";
          document.querySelector(".exl").appendChild(link);
          link.click();
          document.querySelector(".exl").removeChild(link);
        })
        .catch((error) => {
          console.log(error);
          this.$modal.error({
            title: "错误",
            desc: "网络连接错误",
          });
        });
    },

    // 导出数据
    handleExport() {
      let data = {
        title: "数据",
        url: "/system/accountBank/exportList",
        token: getToken(),
        method: "get",
        data: this.queryParams,
      };
      Export(data);
      // this.$confirm("确认导出三绑奖罚数据吗?", "提示", {
      //   confirmButtonText: "确定",
      //   cancelButtonText: "取消",
      //   type: "warning",
      // }).then(() => {
      //   // 导出请求
      //   axios({
      //     method: 'get',
      //     params: this.queryParams,
      //     headers: {
      //       'Authorization': 'Bearer ' + getToken()
      //     },
      //     url: this.baseURL + '/system/reward/exportList',
      //     responseType: 'blob'
      //   }).then((res) => {
      //     const link = document.createElement('a');
      //     let blob = new Blob([res.data], { type: 'application/x-excel' });
      //     link.style.display = 'none';
      //     link.href = URL.createObjectURL(blob);
      //     link.download = this.queryParams.month ? this.queryParams.month + '三绑奖罚.xlsx' : '三绑奖罚.xlsx';
      //     document.querySelector('.exl').appendChild(link);
      //     link.click();
      //     document.querySelector('.exl').removeChild(link);
      //   }).catch(error => {
      //     console.log(error);
      //     this.$modal.error({
      //       title: '错误',
      //       desc: '网络连接错误'
      //     });
      //   });

      // });
    },

    // 导入操作触发
    handleImportCommand(command) {
      switch (command) {
        case "handleImport":
          this.handleImport();
          break;
        case "handleTemplete":
          this.handleTemplete();
          break;
        case "handleExport":
          this.handleExport();
          break;
        default:
          break;
      }
    },

    // 重新绘制表格，防止抖动与滚动条异常
    setDataTable() {
      this.$nextTick(() => {
        this.$refs.dataTable.doLayout();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.drawer_content {
  margin: 0 30px;

  .drawer_footer {
    float: right;
    padding-bottom: 40rpx;
  }
}
</style>
