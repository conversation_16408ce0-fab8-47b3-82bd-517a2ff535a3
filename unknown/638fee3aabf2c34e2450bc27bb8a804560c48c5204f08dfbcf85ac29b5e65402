<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb5">
      <!-- 搜索区域 -->
      <el-col>
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          label-width="80px"
        >
          <el-form-item
            label="部门"
            prop="deptName"
            class="el-form-search-item"
          >
            <el-input
              v-model="queryParams.deptName"
              placeholder="请输入部门名称"
              clearable
              size="small"
            />
          </el-form-item>
          <el-form-item
            label="资源代码"
            prop="code"
            class="el-form-search-item"
          >
            <el-input
              v-model="queryParams.code"
              placeholder="请输入资源代码"
              clearable
              size="small"
            />
          </el-form-item>
          <el-form-item
            label="名称"
            prop="name"
            class="el-form-search-item"
          >
            <el-input
              v-model="queryParams.name"
              placeholder="请输入名称"
              clearable
              size="small"
            />
          </el-form-item>
          <el-form-item
            label="时间范围"
            prop="dateRange"
            class="el-form-search-item"
          >
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
          <el-form-item class="el-form-search-item">
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>

    <!-- 操作按钮区域 -->
    <el-row class="mb8" :gutter="10">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-upload"
          size="mini"
          @click="handleUpload"
        >
          上传行方数据
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >
          导出数据
        </el-button>
      </el-col>
    </el-row>

    <el-table
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%"
      :height="tableHeight"
      ref="table"
    >
      <el-table-column label="#" type="index" width="50" align="center">
        <template slot-scope="scope">
          <span>{{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="code" label="资源代码" align="center" width="120" />
      <el-table-column prop="name" label="名称" align="center" width="150" />
      <el-table-column prop="deptName" label="部门" align="center" width="120" />
      <el-table-column prop="yk" label="一卡" align="center" width="80" />
      <el-table-column prop="yx" label="有效" align="center" width="80" />
      <el-table-column prop="kj" label="快捷" align="center" width="80" />
      <el-table-column prop="dk" label="动卡" align="center" width="80" />
      <el-table-column prop="qw" label="企微" align="center" width="80" />
      <el-table-column prop="kjRatio" label="快捷比例" align="center" width="100">
        <template slot-scope="scope">
          {{ scope.row.kjRatio ? scope.row.kjRatio + '%' : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="dkRatio" label="动卡比例" align="center" width="100">
        <template slot-scope="scope">
          {{ scope.row.dkRatio ? scope.row.dkRatio + '%' : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="qwRatio" label="企微比例" align="center" width="100">
        <template slot-scope="scope">
          {{ scope.row.qwRatio ? scope.row.qwRatio + '%' : '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="day"
        label="最终时间"
        align="center"
        width="160"
      />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :page-sizes="[20, 30, 50, 100]"
      @pagination="getList"
    />

    <!-- 上传数据弹框 -->
    <el-dialog
      title="上传行方数据"
      :visible.sync="uploadDialog"
      width="500px"
      append-to-body
    >
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx,.xls"
        :headers="uploadHeaders"
        :action="uploadUrl"
        :disabled="uploadLoading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :on-error="handleFileError"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">
          只能上传xlsx/xls文件，且不超过10MB
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="uploadDialog = false">取 消</el-button>
        <el-button type="primary" @click="submitUpload" :loading="uploadLoading">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getWorkCardList, uploadWorkCardData, exportWorkCard } from "@/api/business/workCard";
import { getToken } from "@/utils/auth";

export default {
  name: "WorkCard",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      tableData: [],
      // 查询参数
      dateRange: [],
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        deptName: undefined,
        code: undefined,
        name: undefined,
        startTime: undefined,
        endTime: undefined,
      },
      // 上传相关
      uploadDialog: false,
      uploadLoading: false,
      uploadUrl: process.env.VUE_APP_BASE_API + "/business/workCard/upload",
      uploadHeaders: { Authorization: "Bearer " + getToken() },
      tableHeight: 650,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    async getList() {
      this.loading = true;

      // 处理时间参数
      if (this.dateRange && this.dateRange.length === 2) {
        this.queryParams.startTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      }

      try {
        const res = await getWorkCardList(this.queryParams);
        this.tableData = res.rows;
        this.total = res.total;
      } finally {
        this.loading = false;
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.dateRange = [];
      this.handleQuery();
    },
    /** 上传按钮操作 */
    handleUpload() {
      this.uploadDialog = true;
    },
    /** 文件上传中处理 */
    handleFileUploadProgress() {
      this.uploadLoading = true;
    },
    /** 文件上传成功处理 */
    handleFileSuccess(response, file) {
      this.uploadLoading = false;
      if (response.code === 200) {
        this.$modal.msgSuccess("上传成功");
        this.uploadDialog = false;
        this.getList();
      } else {
        this.$modal.msgError(response.msg || "上传失败");
      }
      this.$refs.upload.clearFiles();
    },
    /** 文件上传失败处理 */
    handleFileError() {
      this.uploadLoading = false;
      this.$modal.msgError("上传失败，请重试");
      this.$refs.upload.clearFiles();
    },
    /** 提交上传文件 */
    submitUpload() {
      this.$refs.upload.submit();
    },
    /** 导出数据 */
    handleExport() {
      const params = {
        ...this.queryParams,
        startTime: this.dateRange && this.dateRange.length === 2 ? this.dateRange[0] : undefined,
        endTime: this.dateRange && this.dateRange.length === 2 ? this.dateRange[1] : undefined,
      };
      
      this.$modal.confirm("是否确认导出工作卡数据？").then(() => {
        this.$modal.loading("正在导出数据，请稍候");
        exportWorkCard(params).then((response) => {
          this.$modal.closeLoading();
          const link = document.createElement("a");
          let blob = new Blob([response], {
            type: "application/vnd.ms-excel",
          });
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          link.download = "工作卡数据.xlsx";
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }).catch(() => {
          this.$modal.closeLoading();
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}
.mb5 {
  margin-bottom: 5px;
}
.mb8 {
  margin-top: 20px;
  margin-bottom: 8px;
}
.el-form-search-item {
  margin-bottom: 0;
}
</style>
