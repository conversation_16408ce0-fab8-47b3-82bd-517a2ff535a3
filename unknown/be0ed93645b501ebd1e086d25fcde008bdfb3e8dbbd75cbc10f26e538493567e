<template>
  <div class="nhmileage-container">
    <!-- 搜索表单 -->
    <el-form :inline="true" :model="searchForm" class="search-form">
      <el-form-item label="乘机人姓名">
        <el-input
          v-model="searchForm.nickName"
          placeholder="乘机人姓名"
          clearable
        />
      </el-form-item>
      <el-form-item label="身份证编号">
        <el-input
          v-model="searchForm.idCard"
          placeholder="请输入身份证编号"
          clearable
        />
      </el-form-item>
      <el-form-item label="手机号">
        <el-input
          v-model="searchForm.phone"
          placeholder="请输入手机号"
          clearable
        />
      </el-form-item>

      <el-form-item label="订单状态">
        <el-select
          v-model="searchForm.airportStatus"
          placeholder="请选择订单状态"
          clearable
        >
          <el-option label="正常" :value="0" />
          <el-option label="已改签" :value="1" />
          <el-option label="已退票" :value="2" />
          <el-option label="已退票（里程）" :value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="回款状态">
        <el-select
          v-model="searchForm.priceStatus"
          placeholder="请选择回款状态"
          clearable
        >
          <el-option label="已回款" :value="0" />
          <el-option label="未回款" :value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="销售渠道">
        <el-select
          v-model="searchForm.chanelName"
          placeholder="请选择销售渠道"
          clearable
        >
          <el-option
            v-for="item in channelOptions"
            :key="item.label"
            :label="item.label"
            :value="item.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="订购日期">
        <el-date-picker
          v-model="searchForm.addTime"
          type="date"
          placeholder="选择订购日期"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区 -->
    <div class="operation-area">
      <el-dropdown size="medium" @command="handleImportCommand">
        <el-button plain icon="el-icon-caret-bottom" size="mini"
          >数据导入</el-button
        >
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="handleImport" icon="el-icon-upload"
            >数据导入</el-dropdown-item
          >
          <el-dropdown-item command="handleExport" icon="el-icon-download"
            >数据导出</el-dropdown-item
          >
        </el-dropdown-menu>
      </el-dropdown>
      <el-button type="primary" plain @click="handleAdd">新增订单</el-button>
      <el-button
        type="success"
        plain
        @click="handleBatchPayment"
        :disabled="selection.length === 0"
        >批量回款</el-button
      >
    </div>

    <!-- 表格 -->
    <el-table
      :data="tableData"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        fixed="left"
        type="selection"
        width="55"
        align="center"
      />
      <el-table-column
        prop="addTime"
        label="订购日期"
        width="120"
        align="center"
      >
        <template #default="scope">
          {{ formatDateYMD(scope.row.addTime) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="chanelName"
        label="销售渠道"
        width="120"
        align="center"
      />
      <el-table-column
        prop="nickName"
        label="乘机人姓名"
        width="100"
        align="center"
      />
      <el-table-column
        prop="idCard"
        label="身份证"
        width="180"
        align="center"
      />
      <el-table-column
        prop="airport"
        label="航班信息"
        width="120"
        align="center"
      />
      <el-table-column
        prop="airportPrice"
        label="机票金额"
        width="100"
        align="center"
      />
      <el-table-column
        prop="mileage"
        label="预期里程消耗"
        width="100"
        align="center"
      />
      <el-table-column
        prop="mileageTrue"
        label="实际里程消耗"
        width="100"
        align="center"
      />
      <el-table-column
        prop="taxPrice"
        label="税费"
        width="100"
        align="center"
      />
      <el-table-column
        prop="ticketPrice"
        label="改签费"
        width="100"
        align="center"
      />

      <el-table-column
        prop="airportStatus"
        label="订单状态"
        width="100"
        align="center"
      >
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.airportStatus)">
            {{ getStatusText(scope.row.airportStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="priceStatus"
        label="回款状态"
        width="100"
        align="center"
      >
        <template #default="scope">
          <el-tag :type="scope.row.priceStatus === 0 ? 'success' : 'danger'">
            {{ scope.row.priceStatus === 0 ? "已回款" : "未回款" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" align="center" />
      <el-table-column label="操作" width="200" fixed="right" align="center">
        <template #default="scope">
          <el-button
            v-if="[0, 1].includes(scope.row.airportStatus)"
            type="text"
            size="small"
            @click="handleRefund(scope.row)"
            >退票</el-button
          >

          <el-button
            v-if="scope.row.airportStatus === 2"
            type="text"
            size="small"
            @click="handleMileageRefund(scope.row)"
            >退里程</el-button
          >

          <el-button
            v-if="scope.row.airportStatus === 0"
            type="text"
            size="small"
            @click="handleChangeTicket(scope.row)"
            >改签</el-button
          >

          <el-button type="text" size="small" @click="handleEdit(scope.row)"
            >编辑</el-button
          >
          <el-button type="text" size="small" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加分页组件 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
        :current-page="pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>

    <!-- 新增/编辑弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="700px">
      <el-form
        :model="formData"
        :rules="rules"
        ref="formRef"
        label-width="120px"
        :inline="true"
        class="dialog-form"
      >
        <el-form-item label="乘机人姓名" prop="nickName">
          <el-input v-model="formData.nickName" />
        </el-form-item>
        <el-form-item label="身份证编号" prop="idCard">
          <el-input v-model="formData.idCard" />
        </el-form-item>
        <el-form-item label="机票金额" prop="airportPrice">
          <el-input-number v-model="formData.airportPrice" :precision="2" />
        </el-form-item>
        <el-form-item label="销售渠道" prop="chanelName">
          <el-select v-model="formData.chanelName">
            <el-option
              v-for="item in channelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="预期里程消耗" prop="mileage">
          <el-input-number v-model="formData.mileage" :precision="0" />
        </el-form-item>
        <el-form-item label="实际里程消耗" prop="mileageTrue">
          <el-input-number v-model="formData.mileageTrue" :precision="0" />
        </el-form-item>
        <el-form-item label="税费" prop="taxPrice">
          <el-input-number v-model="formData.taxPrice" :precision="2" />
        </el-form-item>
        <el-form-item label="改签费" prop="ticketPrice">
          <el-input-number v-model="formData.ticketPrice" :precision="2" />
        </el-form-item>

        <el-form-item label="订购日期" prop="addTime">
          <el-date-picker
            v-model="formData.addTime"
            type="date"
            placeholder="选择订购日期"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
        <el-form-item label="航班信息" prop="airport" class="full-width">
          <el-input
            v-model="formData.airport"
            type="textarea"
            placeholder="请输入航班信息"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark" class="full-width">
          <el-input v-model="formData.remark" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </template>
    </el-dialog>

    <!-- 回款确认弹窗 -->
    <el-dialog
      title="确认回款"
      :visible.sync="paymentDialogVisible"
      width="400px"
    >
      <div>确认将选中的 {{ selection.length }} 条订单标记为已回款？</div>
      <template #footer>
        <el-button @click="paymentDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmPayment">确 定</el-button>
      </template>
    </el-dialog>

    <!-- 添加改签弹窗 -->
    <el-dialog
      title="订单改签"
      :visible.sync="changeTicketDialogVisible"
      width="500px"
    >
      <el-form
        :model="changeTicketForm"
        :rules="changeTicketRules"
        ref="changeTicketFormRef"
        label-width="120px"
      >
        <el-form-item label="乘机人姓名">
          <el-input v-model="changeTicketForm.nickName" disabled />
        </el-form-item>
        <el-form-item label="航班信息" class="full-width">
          <el-input v-model="changeTicketForm.airport" type="textarea" />
        </el-form-item>
        <el-form-item label="改签费" prop="ticketPrice">
          <el-input-number
            v-model="changeTicketForm.ticketPrice"
            :precision="2"
            :min="0"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="changeTicketDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmChangeTicket">确 定</el-button>
      </template>
    </el-dialog>

    <!-- 添加导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px">
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">
          <el-button
            type="primary"
            plain
            icon="el-icon-receiving"
            size="mini"
            @click="handleTemplete"
            style="float: right"
            >下载模板</el-button
          >
        </div>
        <div class="el-upload__tip" style="color: red" slot="tip">
          提示：仅允许导入"xls"或"xlsx"格式文件！
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getOrderList,
  addOrder,
  updateOrder,
  deleteOrder,
  updatePaymentStatus,
  exportOrder,
  channellist,
} from "@/api/ticket/nhmileage";
import { formatDateYMD } from "@/utils/index";
import { getToken } from "@/utils/auth";

export default {
  name: "NHMileageManagement",
  data() {
    return {
      // 搜索表单数据
      searchForm: {
        nickName: "",
        idCard: "",
        phone: "",
        airportStatus: "",
        priceStatus: "",
        addTime: "",
        airport: "",
        chanelName: "",
      },
      // 表格数据
      tableData: [],
      // 弹窗显示状态
      dialogVisible: false,
      // 弹窗标题
      dialogTitle: "新增订单",
      // 表单数据
      formData: {
        nickName: "",
        chanelName: "",
        idCard: "",
        airportPrice: 0,
        mileage: 0,
        mileageTrue: 0,
        taxPrice: 0,
        ticketPrice: 0,
        addTime: "",
        remark: "",
        airport: "",
      },
      // 销售渠道选项
      channelOptions: [],
      // 表单校验规则
      rules: {
        chanelName: [
          { required: true, message: "请输入订购人姓名", trigger: "blur" },
        ],
        idCard: [
          { required: true, message: "请输入身份证编号", trigger: "blur" },
          {
            pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
            message: "请输入正确的身份证号码",
            trigger: "blur",
          },
        ],
        airportPrice: [
          { required: true, message: "请输入机票金额", trigger: "blur" },
        ],
        mileage: [
          { required: true, message: "请输入里程消耗", trigger: "blur" },
        ],
        mileageTrue: [
          { required: true, message: "请输入里程消耗", trigger: "blur" },
        ],
        addTime: [
          { required: true, message: "请选择订购日期", trigger: "change" },
        ],
        airport: [
          { required: true, message: "请输入航班信息", trigger: "blur" },
        ],
      },
      // 选中的行
      selection: [],
      // 回款弹窗显示状态
      paymentDialogVisible: false,
      // 改签弹窗显示状态
      changeTicketDialogVisible: false,
      // 改签表单数据
      changeTicketForm: {
        id: null,
        nickName: "",
        airport: "",
        ticketPrice: 0,
      },
      // 改签表单校验规则
      changeTicketRules: {
        ticketPrice: [
          { required: true, message: "请输入改签费", trigger: "blur" },
        ],
      },
      // 导入相关数据
      upload: {
        open: false,
        title: "导入订单数据",
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/info/upload",
      },
      // 添加分页相关数据
      pageNum: 1,
      pageSize: 10,
      total: 0,
    };
  },
  methods: {
    // 格式化年月
    formatDateYMD,
    // 获取表格数据
    async getTableData() {
      try {
        const params = {
          ...this.searchForm,
          pageNum: this.pageNum,
          pageSize: this.pageSize,
        };
        const res = await getOrderList(params);
        if (res.code === 200) {
          this.tableData = res.rows;
          this.total = res.total;
        } else {
          this.$message.error(res.msg || "获取数据失败");
        }
      } catch (error) {
        console.error("获取表格数据失败:", error);
        this.$message.error("获取数据失败");
      }
    },
    // 获取订单状态文本
    getStatusText(status) {
      const statusMap = {
        0: "正常",
        1: "已改签",
        2: "用户已结算",
        3: "已结束",
      };
      return statusMap[status] || "";
    },
    // 获取订单状态对应的tag类型
    getStatusType(status) {
      const typeMap = {
        0: "success", // 正常 - 绿色
        1: "warning", // 已改签 - 黄色
        2: "danger", // 已退票 - 红色
        3: "info", // 已退票（里程）- 灰色
      };
      return typeMap[status] || "info";
    },
    // 搜索
    handleSearch() {
      this.pageNum = 1; // 搜索时重置为第一页
      this.getTableData();
    },
    // 重置搜索
    resetSearch() {
      this.searchForm = {
        nickName: "",
        idCard: "",
        phone: "",
        airportStatus: "",
        priceStatus: "",
        addTime: "",
        airport: "",
        chanelName: "",
      };
      this.pageNum = 1; // 重置分页
      this.getTableData();
    },
    // 新增
    handleAdd() {
      this.dialogTitle = "新增订单";
      this.dialogVisible = true;
      this.formData = {
        nickName: "",
        chanelName: "",
        idCard: "",
        airportPrice: 0,
        mileage: 0,
        mileageTrue: 0,
        taxPrice: 0,
        ticketPrice: 0,
        addTime: "",
        remark: "",
        airport: "",
      };
    },
    // 编辑
    handleEdit(row) {
      this.dialogTitle = "编辑订单";
      this.dialogVisible = true;
      this.formData = { ...row };
    },
    // 删除
    handleDelete(row) {
      this.$confirm("确认删除该订单吗？", "提示", {
        type: "warning",
      })
        .then(async () => {
          try {
            const res = await deleteOrder(row.id);
            if (res.code === 200) {
              this.$message.success("删除成功");
              this.getTableData();
            } else {
              this.$message.error(res.msg || "删除失败");
            }
          } catch (error) {
            console.error("删除失败:", error);
            this.$message.error("删除失败");
          }
        })
        .catch(() => {});
    },
    // 提交表单
    handleSubmit() {
      this.$refs.formRef.validate(async (valid) => {
        if (valid) {
          try {
            const isEdit = this.formData.id !== undefined;
            const res = await (isEdit ? updateOrder : addOrder)(this.formData);

            if (res.code === 200) {
              this.$message.success(isEdit ? "修改成功" : "添加成功");
              this.dialogVisible = false;
              this.getTableData();
            } else {
              this.$message.error(
                res.msg || (isEdit ? "修改失败" : "添加失败")
              );
            }
          } catch (error) {
            console.error(isEdit ? "修改失败:" : "添加失败:", error);
            this.$message.error(isEdit ? "修改失败" : "添加失败");
          }
        }
      });
    },
    // 表格多选变化
    handleSelectionChange(val) {
      this.selection = val;
    },
    // 批量回款
    handleBatchPayment() {
      if (this.selection.length === 0) {
        this.$message.warning("请选择需要回款的订单");
        return;
      }
      // 检查是否包含已回款的订单
      const hasCompleted = this.selection.some(
        (item) => item.priceStatus === 0
      );
      if (hasCompleted) {
        this.$message.warning("选中的订单中包含已回款的订单，请重新选择");
        return;
      }
      this.paymentDialogVisible = true;
    },
    // 确认回款
    async confirmPayment() {
      try {
        const ids = this.selection.map((item) => item.id);
        const res = await updatePaymentStatus(ids);
        if (res.code === 200) {
          this.$message.success("回款操作成功");
          this.paymentDialogVisible = false;
          this.getTableData();
        } else {
          this.$message.error(res.msg || "回款操作失败");
        }
      } catch (error) {
        console.error("回款操作失败:", error);
        this.$message.error("回款操作失败");
      }
    },

    // 处理退票
    async handleRefund(row) {
      try {
        await this.$confirm("确认要退票吗？", "提示", {
          type: "warning",
        });

        // TODO: 调用退票接口
        const res = await updateOrder({
          ...row,
          airportStatus: 2, // 更新为已退票状态
        });

        if (res.code === 200) {
          this.$message.success("退票成功");
          this.getTableData();
        } else {
          this.$message.error(res.msg || "退票失败");
        }
      } catch (error) {
        if (error !== "cancel") {
          console.error("退票失败:", error);
          this.$message.error("退票失败");
        }
      }
    },
    // 处理退里程
    async handleMileageRefund(row) {
      try {
        await this.$confirm("确认要退还里程吗？", "提示", {
          type: "warning",
        });

        // TODO: 调用退里程接口
        const res = await updateOrder({
          ...row,
          airportStatus: 3, // 更新为已退票（里程）状态
        });

        if (res.code === 200) {
          this.$message.success("退还里程成功");
          this.getTableData();
        } else {
          this.$message.error(res.msg || "退还里程失败");
        }
      } catch (error) {
        if (error !== "cancel") {
          console.error("退还里程失败:", error);
          this.$message.error("退还里程失败");
        }
      }
    },
    // 处理改签
    handleChangeTicket(row) {
      this.changeTicketDialogVisible = true;
      this.changeTicketForm = {
        id: row.id,
        nickName: row.nickName,
        airport: row.airport,
        ticketPrice: row.ticketPrice || 0,
      };
    },
    // 确认改签
    async confirmChangeTicket() {
      this.$refs.changeTicketFormRef.validate(async (valid) => {
        if (valid) {
          try {
            const res = await updateOrder({
              id: this.changeTicketForm.id,
              ticketPrice: this.changeTicketForm.ticketPrice,
              airportStatus: 1, // 更新为已改签状态
            });

            if (res.code === 200) {
              this.$message.success("改签成功");
              this.changeTicketDialogVisible = false;
              this.getTableData();
            } else {
              this.$message.error(res.msg || "改签失败");
            }
          } catch (error) {
            console.error("改签失败:", error);
            this.$message.error("改签失败");
          }
        }
      });
    },
    // 处理导入导出命令
    handleImportCommand(command) {
      this[command]();
    },

    // 导入数据
    handleImport() {
      this.upload.open = true;
    },

    // 下载模板
    handleTemplete() {
      window.location.href =
        process.env.VUE_APP_BASE_API + "/system/info/importTemplate";
    },

    // 导出数据
    async handleExport() {
      try {
        await this.$confirm("确认导出所有订单数据吗？", "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });

        const res = await exportOrder(this.searchForm);
        const blob = new Blob([res], { type: "application/vnd.ms-excel" });
        const fileName = "订单数据.xlsx";
        if ("download" in document.createElement("a")) {
          const link = document.createElement("a");
          link.download = fileName;
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          document.body.appendChild(link);
          link.click();
          URL.revokeObjectURL(link.href);
          document.body.removeChild(link);
        } else {
          navigator.msSaveBlob(blob, fileName);
        }
      } catch (error) {
        if (error !== "cancel") {
          console.error("导出失败:", error);
          this.$message.error("导出失败");
        }
      }
    },

    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },

    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$message.success("导入成功");
      this.getTableData();
    },

    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },

    // 处理分页变化
    handlePageChange(page) {
      this.pageNum = page;
      this.getTableData();
    },

    // 处理每页显示条数变化
    handleSizeChange(size) {
      this.pageSize = size;
      this.pageNum = 1; // 重置为第一页
      this.getTableData();
    },

    // 获取渠道列表选项
    async getChannelOptions() {
      const res = await channellist();
      if (res.code === 200) {
        // 将返回的渠道数据转换为下拉选项格式
        this.channelOptions = res.data.map((item) => ({
          value: item.name,
          label: item.name,
        }));
      } else {
        console.error("获取渠道列表失败:", res.msg);
      }
    },
  },
  created() {
    // 页面创建时获取表格数据
    this.getTableData();
    // 获取销售渠道选项
    this.getChannelOptions();
  },
};
</script>

<style lang="scss" scoped>
.nhmileage-container {
  padding: 20px;

  .search-form {
    margin-bottom: 16px;
  }

  .operation-area {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .el-button {
      margin-left: 0;
      &:not(:first-child) {
        margin-left: 10px;
      }
    }
  }

  .dialog-form {
    .el-form-item {
      width: 45%;
      margin-right: 4%;

      &.full-width {
        width: 94%;
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}

/* 深度选择器 */
.dialog-form ::v-deep .el-form-item__content {
  width: calc(100% - 120px);

  .el-input-number,
  .el-date-editor.el-input {
    width: 100%;
  }
}

/* 改签弹窗样式 */
.el-dialog ::v-deep .el-input.is-disabled .el-input__inner {
  color: #606266;
}
</style>
