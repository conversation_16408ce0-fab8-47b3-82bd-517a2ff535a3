<template>
  <div class="app-container">
    <el-row :gutter="24" align="center" justify="center">
      <el-col :span="24">
        <!-- <el-button plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button> -->
      </el-col>
      <!-- <el-col :span="9">
      </el-col>
      <el-col :span="11">
      </el-col> -->
      <!-- <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px" class="el-form-search">
        <el-form-item label="上级表头" prop="upField" class="el-form-search-item">
          <el-select v-model="queryParams.upField" placeholder="请选择" clearable>
            <el-option v-for="item in options1" :key="item.value" :label="item.label" :value="item.label">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="名称" prop="field" class="el-form-search-item">
          <el-input v-model="queryParams.field" placeholder="请输入名称" clearable size="mini"
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form> -->
    </el-row>

    <el-table
      :height="tableHeight"
      v-loading="loading"
      :data="houseVillageList"
    >
      <el-table-column label="#" type="index" width="50" align="center">
        <template scope="scope">
          <span>{{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="项目" align="center" prop="deptName" />

      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <!-- <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button> -->
          <el-button
            size="mini"
            type="text"
            icon="el-icon-setting"
            @click="handleUpdates(scope.row)"
            >同步配置</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!--同步配置  -->
    <el-dialog
      title="同步配置"
      :visible.sync="opens"
      width="500px"
      append-to-body
      close-on-click-modal
      v-dialogDrag
    >
      <el-form ref="forms" :model="forms" label-width="80px">
        <el-form-item label="项目名称">
          <el-select v-model="forms.oldId" placeholder="请选择">
            <el-option
              v-for="item in houseVillageList"
              :key="item.id"
              :label="item.deptName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForms">确 定</el-button>
        <el-button @click="cancels">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 添加或修改房源小区对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="500px"
      append-to-body
      close-on-click-modal
      v-dialogDrag
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="项目名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入项目名" />
        </el-form-item>

        <el-form-item label="启用状态">
          <el-switch v-model="form.delFlag" active-value="0" inactive-value="1">
          </el-switch>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listHouseVillage,
  getHouseVillage,
  delHouseVillage,
  addHouseVillage,
  updateHouseVillage,
  updateHouseVillages,
} from "@/api/house/gzproject";

export default {
  name: "gzproject",
  dicts: ["sys_common_status"],
  data() {
    return {
      value: true,
      // 取值方式
      options: [
        {
          value: "1",
          label: "导入",
        },
        {
          value: "2",
          label: "计算",
        },
      ],
      options1: [],
      options2: [
        {
          value: "1",
          label: "全部",
        },
        {
          value: "2",
          label: "主管",
        },
        {
          value: "3",
          label: "业务员",
        },
      ],
      options3: [],
      // 表格高度
      tableHeight: document.documentElement.clientHeight - 280,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 房源小区表格数据
      houseVillageList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      opens: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
      },
      // 表单参数

      form: {
        name: "",
        delFlag: "1",
      },
      // 同步表单
      forms: {},
      // 表单校验
      rules: {
        name: [{ required: true, message: "项目名不能为空", trigger: "blur" }],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询房源小区列表 */
    getList() {
      this.loading = true;
      listHouseVillage(this.queryParams).then((response) => {
        console.log(response);
        this.houseVillageList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 取消按钮
    cancels() {
      this.opens = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        name: "",
        delFlag: "1",
      };

      this.forms = {};
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    // handleSelectionChange(selection) {
    //   this.ids = selection.map((item) => item.id);
    //   this.single = selection.length !== 1;
    //   this.multiple = !selection.length;
    // },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加项目";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      // this.reset();
      const id = row.id;
      getHouseVillage(id).then((response) => {
        this.form = response.data;
        this.form.delFlag = response.data.delFlag;

        this.open = true;
        this.title = "修改项目";
      });
    },
    handleUpdates(e) {
      this.reset();
      this.forms.newId = e.id;
      this.opens = true;
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        // 将deptList转为字符串
        if (this.form.deptList) {
          this.form.deptList = this.form.deptList.join(",");
        }
        if (valid) {
          if (this.form.id != null) {
            updateHouseVillage(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addHouseVillage(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },

    /** 同步 */
    submitForms() {
      this.$refs["forms"].validate((valid) => {
        if (valid) {
          updateHouseVillages(this.forms).then((response) => {
            this.$modal.msgSuccess("同步成功");
            this.opens = false;
            this.getList();
          });
        }
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id;
      this.$modal
        .confirm("是否确认删除记录？")
        .then(function () {
          return delHouseVillage(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/houseVillage/export",
        {
          ...this.queryParams,
        },
        `houseVillage_${new Date().getTime()}.xlsx`
      );
    },

    // 计数器
    handleChange(e) {
      this.form.sort = e;
    },
  },
};
</script>
