<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb5">
      <!-- 右对齐搜索区域 -->
      <el-col>
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          label-width="80px"
        >
          <el-form-item
            label="部门"
            prop="deptName"
            class="el-form-search-item"
          >
            <el-input
              v-model="queryParams.deptName"
              placeholder="请输入部门名称"
              clearable
              size="small"
            />
          </el-form-item>
          <el-form-item
            label="业务员"
            prop="applyName"
            class="el-form-search-item"
          >
            <el-input
              v-model="queryParams.applyName"
              placeholder="请输入员工姓名"
              clearable
              size="small"
            />
          </el-form-item>
          <el-form-item
            label="发放状态"
            prop="redCard"
            class="el-form-search-item"
          >
            <el-select
              v-model="queryParams.redCard"
              clearable
              placeholder="请选择发放状态"
            >
              <el-option label="待发放" :value="0" />
              <el-option label="已发放" :value="1" />
            </el-select>
          </el-form-item>
          <el-form-item
            label="时间范围"
            prop="dateRange"
            class="el-form-search-item"
          >
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
          <el-form-item class="el-form-search-item">
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>

    <!-- 操作按钮区域 -->
    <el-row class="mb8" :gutter="10">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          size="mini"
          @click="handleResend"
          :disabled="!selectedRows.length"
        >
          一键补发卡卷
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExportDialog"
        >
          导出数据
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-document-copy"
          size="mini"
          @click="handleExportWriteOffDetail"
        >
          导出核销明细
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-s-promotion"
          size="mini"
          @click="handleSendCardListDialog"
        >
          发送一元卡券
        </el-button>
      </el-col>
    </el-row>

    <el-table
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%"
      :height="tableHeight"
      @selection-change="handleSelectionChange"
      ref="table"
    >
      <el-table-column
        type="selection"
        width="55"
        align="center"
        :selectable="checkSelectable"
      />
      <el-table-column label="#" type="index" width="50" align="center">
        <template slot-scope="scope">
          <span>{{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="nickName" label="客户姓名" align="center" />
      <el-table-column
        prop="phonenumber"
        label="客户手机号"
        align="center"
        width="120"
      />
      <!-- <el-table-column prop="orderId" label="订单ID" align="center" /> -->

      <el-table-column prop="applyName" label="业务员" align="center" />
      <el-table-column prop="deptName" label="所属部门" align="center" />
      <el-table-column prop="redCard" label="发放状态" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.redCard ? 'success' : 'primary'">
            {{ scope.row.redCard ? "已发放" : "待发放" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="orderAmount" label="红包金额" align="center">
        <template slot-scope="scope">
          {{
            scope.row.orderAmount ? scope.row.orderAmount + "元" : "谢谢惠顾"
          }}
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="发放时间"
        align="center"
        width="160"
      />
      <el-table-column label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-button
            v-if="!scope.row.redCard"
            size="mini"
            type="text"
            icon="el-icon-s-promotion"
            @click="handleEdit(scope.row)"
            >补发卡卷</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :page-sizes="[20, 30, 50, 100]"
      @pagination="getList"
    />

    <!-- 添加导出选项弹框 -->
    <el-dialog
      title="导出选项"
      :visible.sync="exportDialog"
      width="500px"
      append-to-body
    >
      <el-form
        ref="exportForm"
        :model="exportForm"
        :rules="exportRules"
        label-width="80px"
      >
        <el-form-item label="时间范围" prop="dateRange">
          <el-date-picker
            v-model="exportForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="导出类型" prop="exportType">
          <el-select
            v-model="exportForm.exportType"
            placeholder="请选择导出类型"
            style="width: 100%"
          >
            <el-option label="客户领取明细" value="customer" />
            <el-option label="员工发卷明细" value="staff" />
            <el-option label="项目组发卷明细" value="dept" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="exportDialog = false">取 消</el-button>
        <el-button type="primary" @click="confirmExport">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 发送卡券列表弹框 -->
    <el-dialog
      title="发送卡券列表"
      :visible.sync="sendCardListDialog"
      width="400px"
      append-to-body
    >
      <el-form
        ref="sendCardListForm"
        :model="sendCardListForm"
        :rules="sendCardListRules"
        label-width="80px"
      >
        <el-form-item label="批次码" prop="stockId">
          <el-input
            v-model="sendCardListForm.stockId"
            placeholder="请输入批次码"
            clearable
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="sendCardListDialog = false">取 消</el-button>
        <el-button type="primary" @click="confirmSendCardList">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import axios from "axios";
import { getToken } from "@/utils/auth";
import { getCurlsList, sendCard, sendCardList } from "@/api/system/curls";

export default {
  name: "Curls",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 总条数
      total: 0,
      // 表格数据
      tableData: [],
      // 查询参数
      dateRange: [],
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        deptName: undefined,
        applyName: undefined,
        redCard: undefined,
        startTime: undefined,
        endTime: undefined,
      },
      // 导出弹框显示状态
      exportDialog: false,
      // 导出表单参数
      exportForm: {
        dateRange: [],
        exportType: undefined,
      },
      // 导出表单校验规则
      exportRules: {
        dateRange: [
          { required: true, message: "请选择时间范围", trigger: "change" },
        ],
        exportType: [
          { required: true, message: "请选择导出类型", trigger: "change" },
        ],
      },
      tableHeight: 650,
      selectedRows: [], // 新增选中行数组
      // 发送卡券列表弹框显示状态
      sendCardListDialog: false,
      // 发送卡券列表表单参数
      sendCardListForm: {
        stockId: "",
      },
      // 发送卡券列表表单校验规则
      sendCardListRules: {
        stockId: [{ required: true, message: "请输入批次码", trigger: "blur" }],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    async getList() {
      this.loading = true;

      // 处理时间参数
      if (this.dateRange && this.dateRange.length === 2) {
        this.queryParams.startTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      }

      try {
        const res = await getCurlsList(this.queryParams);
        this.tableData = res.rows;
        this.total = res.total;
      } finally {
        this.loading = false;
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.dateRange = [];
      this.selectedRows = [];
      if (this.$refs.table) {
        this.$refs.table.clearSelection();
      }
      this.handleQuery();
    },
    /** 补发按钮操作 */
    handleEdit(row) {
      this.$modal
        .confirm(`确认要对"${row.nickName}"进行补发操作吗？`)
        .then(async () => {
          try {
            this.$modal.loading("正在补发中，请稍候...");
            // 调用补发接口，单个补发时传入单个id的数组
            await sendCard({ list: [row.openid] });
            this.$modal.msgSuccess("补发成功");
            this.getList();
          } catch (error) {
            console.error("补发失败:", error);
            this.$modal.msgError("补发失败，请重试");
          } finally {
            this.$modal.closeLoading();
          }
        })
        .catch(() => {});
    },
    /** 表格多选框选中数据 */
    handleSelectionChange(selection) {
      this.selectedRows = selection.filter((row) => !row.redCard);
    },
    /** 一键补发按钮操作 */
    handleResend() {
      if (!this.selectedRows.length) {
        this.$modal.msgError("请先选择要补发的客户");
        return;
      }

      const names = this.selectedRows.map((item) => item.nickName).join("、");
      this.$modal
        .confirm(`确认要对"${names}"进行补发操作吗？`)
        .then(async () => {
          try {
            this.$modal.loading("正在补发中，请稍候...");
            // 调用补发接口，传入选中行的openid数组
            const openids = this.selectedRows.map((item) => item.openid);
            await sendCard({ list: openids });
            this.$modal.msgSuccess("补发成功");
            this.getList();
          } catch (error) {
            console.error("补发失败:", error);
            this.$modal.msgError("补发失败，请重试");
          } finally {
            this.$modal.closeLoading();
          }
        })
        .catch(() => {});
    },
    /** 显示导出弹框 */
    handleExportDialog() {
      this.exportDialog = true;
      this.exportForm = {
        dateRange: [],
        exportType: undefined,
      };
    },
    /** 确认导出操作 */
    confirmExport() {
      this.$refs.exportForm.validate((valid) => {
        if (valid) {
          const params = {
            startTime: this.exportForm.dateRange[0],
            endTime: this.exportForm.dateRange[1],
          };

          this.$modal.confirm("是否确认导出数据？").then(() => {
            this.$modal.loading("正在导出数据，请稍候");

            // 根据选择的导出类型确定接口地址
            const urlMap = {
              customer: "/system/curls/export",
              staff: "/system/curls/exportStaff",
              dept: "/system/curls/exportDept",
            };

            // 根据选择的导出类型确定文件名
            const fileNameMap = {
              customer: `${params.startTime}-${params.endTime}客户领取明细`,
              staff: `${params.startTime}-${params.endTime}员工发卷明细`,
              dept: `${params.startTime}-${params.endTime}项目组发卷明细`,
            };

            axios({
              method: "post",
              headers: {
                Authorization: "Bearer " + getToken(),
              },
              url:
                process.env.VUE_APP_BASE_API +
                urlMap[this.exportForm.exportType],
              data: params,
              responseType: "blob",
            })
              .then((res) => {
                this.$modal.closeLoading();
                const link = document.createElement("a");
                let blob = new Blob([res.data], {
                  type: "application/vnd.ms-excel",
                });
                link.style.display = "none";
                link.href = URL.createObjectURL(blob);
                link.download =
                  fileNameMap[this.exportForm.exportType] + ".xlsx";
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                this.exportDialog = false;
              })
              .catch(() => {
                this.$modal.closeLoading();
              });
          });
        }
      });
    },
    /** 导出核销明细 */
    handleExportWriteOffDetail() {
      this.$modal.loading("正在导出核销明细，请稍候");

      axios({
        method: "post",
        headers: {
          Authorization: "Bearer " + getToken(),
        },
        url: process.env.VUE_APP_BASE_API + "/business/workData/exportCard",
        responseType: "blob",
      })
        .then((res) => {
          this.$modal.closeLoading();
          const link = document.createElement("a");
          let blob = new Blob([res.data], {
            type: "application/vnd.ms-excel",
          });
          link.style.display = "none";
          link.href = URL.createObjectURL(blob);
          link.download = "核销明细.xlsx";
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          this.$modal.msgSuccess("导出成功");
        })
        .catch(() => {
          this.$modal.closeLoading();
          this.$modal.msgError("导出失败，请重试");
        });
    },
    /** 判断行是否可选 */
    checkSelectable(row) {
      return !row.redCard; // redCard为1时返回false，表示不可选
    },
    /** 显示发送卡券列表弹框 */
    handleSendCardListDialog() {
      this.sendCardListDialog = true;
      this.sendCardListForm = {
        stockId: "",
      };
    },
    /** 确认发送卡券列表操作 */
    confirmSendCardList() {
      this.$refs.sendCardListForm.validate(async (valid) => {
        if (valid) {
          try {
            this.$modal.loading("正在发送卡券列表，请稍候...");
            await sendCardList(this.sendCardListForm.stockId);
            this.$modal.msgSuccess("发送卡券列表成功");
            this.sendCardListDialog = false;
          } catch (error) {
            console.error("发送卡券列表失败:", error);
            this.$modal.msgError("发送卡券列表失败，请重试");
          } finally {
            this.$modal.closeLoading();
          }
        }
      });
    },
  },
  watch: {
    // 监听数据变化，清空选中状态
    tableData() {
      this.$nextTick(() => {
        if (this.$refs.table) {
          this.$refs.table.clearSelection();
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}
.mb5 {
  margin-bottom: 5px;
}
.mb8 {
  margin-top: 20px;
  margin-bottom: 8px;
}
.el-form-search-item {
  margin-bottom: 0;
}
</style>
