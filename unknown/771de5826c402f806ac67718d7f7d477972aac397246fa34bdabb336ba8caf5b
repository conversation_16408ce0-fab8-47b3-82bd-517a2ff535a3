<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb5">
      <el-col :span="1.5">
        <el-button plain icon="el-icon-plus" size="mini" @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-dropdown @command="handleCommand">
          <el-button plain size="mini">
            导入导出<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="import" icon="el-icon-upload"
              >数据导入</el-dropdown-item
            >
            <el-dropdown-item command="export" icon="el-icon-download"
              >数据导出</el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown>
      </el-col>

      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        label-width="80px"
        class="el-form-search"
      >
        <el-form-item
          label="部门名称"
          prop="deptName"
          class="el-form-search-item"
        >
          <el-input
            v-model="queryParams.deptName"
            placeholder="请输入部门名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="人数" prop="staffNum" class="el-form-search-item">
          <el-input
            v-model="queryParams.staffNum"
            placeholder="请输入人数"
          ></el-input>
        </el-form-item>
        <el-form-item label="月份" prop="month" class="el-form-search-item">
          <el-date-picker
            :clearable="false"
            v-model="queryParams.month"
            format="yyyy 年 MM 月"
            value-format="yyyy-MM"
            type="month"
            placeholder="请选择月份"
          >
          </el-date-picker>
        </el-form-item>

        <el-form-item class="el-form-search-item">
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
    </el-row>

    <el-table
      :height="tableHeight"
      v-loading="loading"
      :data="deptList"
      ref="dataTable"
    >
      <el-table-column label="#" type="index" width="50" align="center">
        <template slot-scope="scope">
          <span>{{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="deptName" label="部门名称" align="center" />
      <el-table-column prop="staffNum" label="人数" align="center" />
      <el-table-column prop="month" label="月份" align="center" />
      <el-table-column label="操作" align="center" width="180">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button size="mini" type="text" @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button size="mini" type="text" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="部门名称" prop="deptName">
          <el-input v-model="form.deptName" placeholder="请输入部门名称" />
        </el-form-item>
        <el-form-item label="人数" prop="staffNum">
          <el-input v-model="form.staffNum" placeholder="请输入人数" />
        </el-form-item>
        <el-form-item label="月份" prop="month">
          <el-date-picker
            v-model="form.month"
            format="yyyy 年 MM 月"
            value-format="yyyy-MM"
            type="month"
            placeholder="请选择月份"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 部门详情对话框 -->
    <el-dialog
      title="部门详情"
      :visible.sync="detailOpen"
      width="500px"
      append-to-body
    >
      <el-descriptions :column="1" border>
        <el-descriptions-item label="部门名称">{{
          deptDetail.deptName
        }}</el-descriptions-item>
        <el-descriptions-item label="人数">{{
          deptDetail.staffNum
        }}</el-descriptions-item>
        <el-descriptions-item label="月份">{{
          deptDetail.month
        }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog
      title="导入部门数据"
      :visible.sync="importOpen"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="uploadHeaders"
        :action="uploadUrl"
        :disabled="uploadLoading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>

        <div class="el-upload__tip" style="color: red" slot="tip">
          提示：仅允许导入"xls"或"xlsx"格式文件！
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="submitFileForm"
          :loading="uploadLoading"
          >确 定</el-button
        >
        <el-button @click="importOpen = false">取 消</el-button>
      </div>
      <div class="exl"></div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getDeptList,
  getDept,
  addDept,
  updateDept,
  delDept,
  exportDept,
  importDept,
} from "@/api/revenue/dept";
import { getToken } from "@/utils/auth";

export default {
  name: "DeptManagement",
  dicts: ["sys_notice_status"],
  data() {
    return {
      baseURL: process.env.VUE_APP_BASE_API,
      // 表格高度
      tableHeight: document.documentElement.clientHeight - 280,
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 部门列表数据
      deptList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示详情弹出层
      detailOpen: false,
      // 部门详情数据
      deptDetail: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        deptName: undefined,
        staffNum: undefined,
        month: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        deptName: [
          { required: true, message: "部门名称不能为空", trigger: "blur" },
        ],
        staffNum: [
          { required: true, message: "人数不能为空", trigger: "blur" },
        ],
        month: [{ required: true, message: "月份不能为空", trigger: "change" }],
      },
      // 导入相关参数
      importOpen: false,
      uploadLoading: false,
      uploadUrl: process.env.VUE_APP_BASE_API + "/business/accountDept/upload",
      uploadHeaders: { Authorization: "Bearer " + getToken() },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询部门列表 */
    getList() {
      this.loading = true;
      getDeptList(this.queryParams).then((response) => {
        this.deptList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    /** 查询部门详情 */
    getDeptDetail(id) {
      getDept(id).then((response) => {
        this.deptDetail = response.data;
        this.detailOpen = true;
      });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        deptName: undefined,
        staffNum: undefined,
        month: undefined,
      };
      this.resetForm("form");
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加部门";
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getDept(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改部门";
      });
    },

    /** 查看详情按钮操作 */
    handleView(row) {
      this.getDeptDetail(row.id);
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id) {
            updateDept(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDept(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm("是否确认删除该部门数据？")
        .then(function () {
          return delDept(row.id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },

    // 重新绘制表格，防止抖动与滚动条异常
    setDataTable() {
      this.$nextTick(() => {
        this.$refs.dataTable.doLayout();
      });
    },

    /** 处理下拉菜单命令 */
    handleCommand(command) {
      if (command === "export") {
        this.handleExport();
      } else if (command === "import") {
        this.handleImport();
      }
    },

    /** 导出按钮操作 */
    handleExport() {
      try {
        const loading = this.$loading({
          lock: true,
          text: "正在导出数据，请稍候...",
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.7)",
        });

        exportDept(this.queryParams)
          .then((response) => {
            this.downloadFile(response, "部门数据.xlsx");
            loading.close();
            this.$modal.msgSuccess("导出成功");
          })
          .catch(() => {
            loading.close();
            this.$modal.msgError("导出失败");
          });
      } catch (error) {
        console.error("导出失败:", error);
        this.$modal.msgError("导出失败");
      }
    },

    /** 下载文件 */
    downloadFile(response, fileName) {
      const blob = new Blob([response], { type: "application/vnd.ms-excel" });
      if ("download" in document.createElement("a")) {
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = fileName;
        link.style.display = "none";
        document.body.appendChild(link);
        link.click();
        URL.revokeObjectURL(link.href);
        document.body.removeChild(link);
      } else {
        navigator.msSaveBlob(blob, fileName);
      }
    },

    /** 导入按钮操作 */
    handleImport() {
      this.importOpen = true;
    },

    // 文件上传中处理
    handleFileUploadProgress() {
      this.uploadLoading = true;
    },

    // 文件上传成功处理
    handleFileSuccess(response) {
      this.uploadLoading = false;
      this.importOpen = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg || "导入成功", "导入结果", {
        dangerouslyUseHTMLString: true,
      });
      this.getList();
    },

    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.mb5 {
  margin-bottom: 5px;
}

.el-form-search-item {
  margin-bottom: 10px;
}

.drawer_content {
  margin: 0 30px;

  .drawer_footer {
    float: right;
    padding-bottom: 40rpx;
  }
}

.exl {
  display: none;
}
</style>
