<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb5">
      <el-col :span="1.5">
        <el-button
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:houseVillage:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="multiple"
          @click="handleUpdates"
          v-hasPermi="['system:address:edit']"
          >同表头批量修改</el-button
        >
      </el-col>
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        label-width="68px"
        class="el-form-search"
      >
        <el-form-item label="项目组" prop="deptId" class="el-form-search-item">
          <el-select v-model="queryParams.deptId" placeholder="请选择项目组">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="规则选项"
          prop="calcType"
          class="el-form-search-item"
        >
          <el-select
            v-model="queryParams.calcType"
            placeholder="请选择"
            @change="onChange"
          >
            <el-option
              v-for="item in options2"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="规则表头"
          prop="fieldId"
          class="el-form-search-item"
        >
          <el-select v-model="queryParams.fieldId" placeholder="请选择">
            <el-option
              v-for="item in options1"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="el-form-search-item">
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
    </el-row>

    <el-table
      :height="tableHeight"
      v-loading="loading"
      :data="houseVillageList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="#" type="index" width="50" align="center">
        <template scope="scope">
          <span>{{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="项目组" align="center" prop="deptName" />
      <!-- 隐藏掉 -->
      <el-table-column
        v-if="false"
        label="项目ID"
        align="center"
        prop="deptId"
      />
      <el-table-column
        v-if="false"
        label="规则表头ID"
        align="center"
        prop="fieldId"
      />
      <el-table-column label="规则表头" align="center" prop="field" />
      <el-table-column label="规则选项" align="center" prop="calcName" />
      <el-table-column label="范围规则关联表头" align="center" prop="reField">
        <template slot-scope="scope">
          {{ scope.row.reField ? scope.row.reField : "无" }}
        </template>
      </el-table-column>
      <el-table-column label="范围规则" align="center" prop="reFieldRule">
        <template slot-scope="scope">
          {{ scope.row.reFieldRule ? scope.row.reFieldRule : "无" }}
        </template>
      </el-table-column>
      <el-table-column label="计算规则" align="center" prop="calcFormula">
        <template slot-scope="scope">
          {{ scope.row.calcFormula ? scope.row.calcFormula : "无" }}
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:houseVillage:edit']"
            >修改</el-button
          >

          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:houseVillage:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改房源小区对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="700px"
      append-to-body
      close-on-click-modal
      v-dialogDrag
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="选择项目组" prop="deptList">
              <el-select
                v-model="form.deptList"
                multiple
                :disabled="form.id == null ? false : true"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="规则表头" prop="fieldId">
              <el-select
                v-model="form.fieldId"
                placeholder="请选择"
                :disabled="form.idList ? true : false"
              >
                <el-option
                  v-for="item in options1"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="12">
            <!-- 选择使用什么规则 -->
            <el-form-item label="规则选项" prop="calcType">
              <el-select
                v-model="form.calcType"
                placeholder="请选择"
                @change="onChange"
              >
                <el-option
                  v-for="item in options2"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 范围规则关联字段 -->
            <el-form-item
              v-if="form.calcType == 2 || form.calcType == 3"
              label="关联字段"
              prop="reFieldId"
            >
              <el-select v-model="form.reFieldId" placeholder="请选择">
                <el-option
                  v-for="item in options3"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 计算规则 -->
        <div v-if="form.calcType == 1">
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="计算规则" prop="calcFormula">
                <el-input
                  type="textarea"
                  v-model="form.calcFormula"
                  placeholder="点击下方按钮输入内容"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <div style="display: flex">
            <div>
              <el-button
                v-for="item in options4"
                :key="item.value"
                plain
                size="mini"
                @click="Calculate(item.label)"
                style="margin-left: 10px; margin-bottom: 5px"
              >
                {{ item.label }}
              </el-button>
              <el-button
                v-for="item in HeaderList"
                :key="item.value"
                :type="item.type == 1 ? 'success' : 'primary'"
                plain
                size="mini"
                @click="Calculate(item.label)"
                style="margin-left: 10px; margin-bottom: 5px"
              >
                {{ item.label }}
              </el-button>
            </div>

            <div
              style="
                width: 30%;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
              "
            >
              <div
                style="
                  display: flex;
                  width: 100px;
                  justify-content: space-around;
                  margin-bottom: 5px;
                "
              >
                <el-button @click="Calculate('+')" style="margin-left: 0">
                  +
                </el-button>
                <el-button @click="Calculate('-')" style="margin-left: 0">
                  -
                </el-button>
              </div>
              <div
                style="
                  display: flex;
                  width: 100px;
                  justify-content: space-around;
                  margin-bottom: 5px;
                "
              >
                <el-button @click="Calculate('*')" style="margin-left: 0">
                  *
                </el-button>
                <el-button @click="Calculate('/')" style="margin-left: 0">
                  /
                </el-button>
              </div>
              <div
                style="
                  display: flex;
                  width: 100px;
                  justify-content: space-around;
                  margin-bottom: 5px;
                "
              >
                <el-button @click="Calculate('(')" style="margin-left: 0">
                  (
                </el-button>
                <el-button @click="Calculate(')')" style="margin-left: 0">
                  )
                </el-button>
              </div>

              <div
                style="
                  display: flex;
                  width: 100px;
                  justify-content: space-around;
                  margin-bottom: 5px;
                "
              >
                <el-button
                  @click="Calculate('回删')"
                  style="margin-left: 0; width: 90px"
                  type="info"
                >
                  回删
                </el-button>
              </div>
              <div
                style="
                  display: flex;
                  width: 100px;
                  justify-content: space-around;
                  margin-bottom: 5px;
                "
              >
                <el-button
                  @click="Calculate('清空')"
                  style="margin-left: 0; width: 90px"
                  type="danger"
                >
                  清空
                </el-button>
              </div>
            </div>
          </div>
        </div>
        <!-- 范围规则 -->
        <div v-if="form.calcType == 2">
          <el-button
            style="transform: translateY(63px)"
            size="mini"
            type="primary"
            @click="addDomain"
            >新增范围</el-button
          >
          <el-form-item
            v-for="(domain, index) in form.scopeList"
            :label="'范围规则' + index"
            :key="domain.key"
            :prop="'scopeList.' + index + '.minValue'"
            :rules="{
              required: true,
              message: '范围规则不能为空',
              trigger: 'blur',
            }"
          >
            <el-row :gutter="24" style="transform: translateX(-12px)">
              <el-col :span="7">
                最小值
                <el-input type="number" v-model="domain.minValue"></el-input>
              </el-col>
              <el-col :span="7">
                最大值
                <el-input type="number" v-model="domain.maxValue"></el-input>
              </el-col>
              <el-col :span="7">
                佣金/件
                <el-input type="number" v-model="domain.endValue"></el-input>
              </el-col>
              <el-button
                type="danger"
                icon="el-icon-delete"
                circle
                style="transform: translateY(31px)"
                @click.prevent="removeDomain(domain)"
              ></el-button>
            </el-row>
          </el-form-item>
        </div>

        <!-- 累加规则 -->
        <div v-if="form.calcType == 3">
          <el-button
            style="transform: translateY(63px)"
            size="mini"
            type="primary"
            @click="addDomain"
            >新增范围</el-button
          >
          <el-form-item
            v-for="(domain, index) in form.scopeList"
            :label="'累加规则' + index"
            :key="domain.key"
            :prop="'scopeList.' + index + '.minValue'"
            :rules="{
              required: true,
              message: '累加规则不能为空',
              trigger: 'blur',
            }"
          >
            <el-row :gutter="24" style="transform: translateX(-12px)">
              <el-col :span="5">
                最小值
                <el-input type="number" v-model="domain.minValue"></el-input>
              </el-col>
              <el-col :span="5">
                最大值
                <el-input type="number" v-model="domain.maxValue"></el-input>
              </el-col>
              <el-col :span="5">
                累加取值
                <el-input type="number" v-model="domain.endValue"></el-input>
              </el-col>
              <el-col :span="5">
                佣金值
                <el-input type="number" v-model="domain.yjPrice"></el-input>
              </el-col>
              <el-button
                type="danger"
                icon="el-icon-delete"
                circle
                style="transform: translateY(31px)"
                @click.prevent="removeDomain(domain)"
              ></el-button>
            </el-row>
          </el-form-item>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listHouseVillage,
  getHouseVillage,
  delHouseVillage,
  addHouseVillage,
  updateHouseVillage,
  deptList,
  MeterHeadList,
  updateHouseVillages,
} from "@/api/house/houseRules";

export default {
  name: "HouseVillage",
  dicts: ["sys_common_status"],
  data() {
    return {
      // 取值方式
      options: [
        {
          value: 1,
          label: "导入",
        },
        {
          value: 2,
          label: "计算",
        },
      ],
      options1: [
        {
          value: 1,
          label: "基本工资",
        },
        {
          value: 2,
          label: "中信",
        },
        {
          value: 3,
          label: "广发",
        },
        {
          value: 4,
          label: "浦发",
        },
      ],
      options2: [
        {
          value: "1",
          label: "计算规则",
        },
        {
          value: "2",
          label: "范围规则",
        },
        {
          value: "3",
          label: "累加规则",
        },
      ],
      options3: [],

      HeaderList: [],
      selectedOptions: [],
      // 表格高度
      tableHeight: document.documentElement.clientHeight - 280,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 规则表格数据
      houseVillageList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
      },
      // 表单参数
      num: 1,
      // 项目组列表
      deptIds: [],
      // 项目组ids
      idList: [],
      // 多选表头id
      fieldId: "",
      // 动态表单
      form: {
        deptList: [],
        fieldId: "",
        calcType: "",
        reFieldId: "",
        calcFormula: "",
        scopeList: [
          {
            minValue: "",
            maxValue: "",
            endValue: "",
          },
        ],
      },
      // 计算规则数组
      rulesList: [],
      // 表单校验
      rules: {
        field: [{ required: true, message: "表头名不能为空", trigger: "blur" }],
        deptList: [
          { required: true, message: "取值方式不能为空", trigger: "blur" },
        ],
        fieldId: [
          { required: true, message: "挂钩标识不能为空", trigger: "blur" },
        ],
        reFieldId: [
          { required: true, message: "挂钩标识不能为空", trigger: "blur" },
        ],
        calcFormula: [
          { required: true, message: "计算规则不能为空", trigger: "blur" },
        ],
        calcType: [{ required: true, message: "请选择规则", trigger: "blur" }],
      },
      // 项目组列表
      options: [],
    };
  },
  created() {
    this.getMeterHeadList();
    this.getDeptList();
    this.getMeterHeadAllList();
    this.getList();
  },
  methods: {
    // 改变规则时 清空规则表单
    onChange() {
      this.form.reFieldId = "";
      this.form.calcFormula = "";
      this.form.scopeList = [
        {
          minValue: "",
          maxValue: "",
          endValue: "",
        },
      ];
    },

    // 删除一项
    removeDomain(item) {
      var index = this.form.scopeList.indexOf(item);
      if (this.form.scopeList.length == 1) {
        return this.$modal.msgError("至少保留一项");
      } else {
        if (index !== -1) {
          this.form.scopeList.splice(index, 1);
        }
      }
    },
    // 添加一项
    addDomain() {
      this.form.scopeList.push({
        minValue: "",
        maxValue: "",
        endValue: "",
        key: Date.now(),
      });
    },

    //计算器
    Calculate(e) {
      if (e == "清空") {
        this.rulesList = [];
      } else if (e == "回删") {
        this.rulesList = this.rulesList.slice(0, -1);
      } else {
        console.log(e);
        this.rulesList.push(e);
      }
      this.form.calcFormula = this.rulesList.join("");
    },
    /** 查询项目组列表 */
    getDeptList() {
      this.loading = true;
      deptList().then((response) => {
        this.options = response.data.map((item) => {
          return { label: item.deptName, value: item.deptId };
        });
      });
    },

    /** 查询计算下拉表头 */
    getMeterHeadList() {
      this.loading = true;
      MeterHeadList(2).then((response) => {
        this.options1 = response.data.map((item) => {
          return { label: item.field, value: item.id };
        });
      });
      MeterHeadList(1).then((response) => {
        this.options3 = response.data.map((item) => {
          return { label: item.field, value: item.id };
        });
      });
    },

    /** 查询所有表头 */
    getMeterHeadAllList() {
      this.loading = true;
      MeterHeadList("").then((response) => {
        this.HeaderList = response.data.map((item) => {
          return { label: item.field, value: item.id, type: item.type };
        });
      });
    },

    /** 查询项目组列表 */
    getDeptList() {
      this.loading = true;
      deptList().then((response) => {
        this.options = response.data.map((item) => {
          return { label: item.deptName, value: item.deptId };
        });
      });
    },

    /** 查询规则列表 */
    getList() {
      this.loading = true;
      listHouseVillage(this.queryParams).then((response) => {
        this.houseVillageList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        deptList: [],
        fieldId: null,
        calcType: null,
        reFieldId: null,
        calcFormula: null,
        scopeList: [
          {
            minValue: "",
            maxValue: "",
            endValue: "",
          },
        ],
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.multiple = true;
      this.deptIds = selection.map((item) => item.deptId);
      this.idList = selection.map((item) => item.id);
      this.fieldId = selection.map((item) => item.fieldId)[0];
      if (this.queryParams.fieldId) {
        this.multiple = !selection.length;
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加规则";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getHouseVillage(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改规则";
      });
    },
    /** 批量修改按钮操作 */
    handleUpdates() {
      this.reset();
      this.form.deptList = this.deptIds;
      this.form.idList = this.idList;
      this.form.fieldId = this.fieldId;
      this.title = "批量修改规则";
      this.open = true;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.idList) {
            updateHouseVillages(this.form).then((response) => {
              this.$modal.msgSuccess("批量修改成功");
              this.open = false;
              this.getList();
            });
          } else if (this.form.id != null) {
            updateHouseVillage(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addHouseVillage(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm("是否确认删除记录？")
        .then(function () {
          return delHouseVillage(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/houseVillage/export",
        {
          ...this.queryParams,
        },
        `houseVillage_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>

<style>
.el-textarea.is-disabled .el-textarea__inner {
  color: #606266;
}
</style>
