export const titleList = [
  { label: "时间", value: "month" },
  { label: "归属部门", value: "group" },
  { label: "银行", value: "bankType" },
  { label: "主营业务收入", value: "sum" },
  { label: "本月收入（含税）", value: "tax" },
  { label: "营业税金及附加", value: "subTax" },
  { label: "增值税", value: "vatTax" },
  { label: "附加税", value: "addTax" },
  { label: "主营业务利润", value: "profit" },
  { label: "其他业务利润", value: "addProfit" },
  { label: "营业费用", value: "subOperatingReceipt" },
  { label: "变动营业费用", value: "operatingReceipt" },
  { label: "工资", value: "sumSalary" },
  { label: "社保费", value: "socialSecurity" },
  { label: "差旅费", value: "travelBusiness" },
  { label: "网银转账手续费", value: "bankingTransfer" },
  { label: "市内交通费", value: "transportFee" },
  { label: "招聘费", value: "recruitmentExpense" },
  { label: "邮寄费", value: "postage" },
  { label: "招待费", value: "entertainExpense" },
  { label: "办公费", value: "officeFee" },
  { label: "办公用品", value: "officeSupplies" },
  { label: "福利费", value: "welfareFunds" },
  { label: "奖励", value: "award" },
  { label: "团建费", value: "leagueBuildingFee" },
  { label: "防疫费用", value: "antiepidemicFee" },
  { label: "项目", value: "costumeFee" },
  { label: "佣金", value: "commission" },
  { label: "其他(变动营业利润)", value: "otherProfit" },
  { label: "固定费用", value: "fixedCharge" },
  { label: "场地租赁费", value: "spaceFee" },
  { label: "员工宿舍租赁费", value: "dormitoryExpenses" },
  { label: "财务费用", value: "finance" },
  { label: "网银转账手续费", value: "bankTransferFee" },
  { label: "网银服务费", value: "serviceFee" },
  { label: "管理费用", value: "manageFee" },
  { label: "工资", value: "salary" },
  { label: "社保费", value: "socialSecurity2" },
  { label: "差旅费", value: "travelFee" },
  { label: "车辆使用费", value: "carFee" },
  { label: "邮寄费", value: "postage3" },
  { label: "招待费", value: "serveFee" },
  { label: "办公费", value: "workFee" },
  { label: "福利费", value: "welfareFee" },
  { label: "办公用品", value: "officeSuppliesFee" },
  { label: "会议经费", value: "conference" },
  { label: "办公室租赁费/物业费", value: "propertyFee" },
  { label: "员工宿舍租赁费", value: "dormitoryFee" },
  { label: "电话费/宽带费", value: "phoneFee" },
  { label: "水电费", value: "utilityBills" },
  { label: "其他(管理费用)", value: "otherFee" },
  { label: "营业利润", value: "operatingProfit" },
  { label: "投资收益", value: "investment" },
  { label: "补贴收入", value: "subsidy" },
  { label: "营业外收入", value: "nonbusiness" },
  { label: "营业外支出", value: "expenditure" },
  { label: "以前年度损益调整", value: "adjustment" },
  { label: "利润总额", value: "returnSum" },
  { label: "所得税", value: "incomeTax" },
  { label: "净利润", value: "retainedProfits" },
  { label: "备注", value: "remark" },
  { label: "资源成本", value: "resourceCost" },
];
