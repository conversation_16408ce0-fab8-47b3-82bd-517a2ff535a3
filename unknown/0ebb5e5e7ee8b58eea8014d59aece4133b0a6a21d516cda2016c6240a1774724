<template>
  <div class="app-container">
    <el-row :gutter="24" align="center" justify="center">
      <el-col :span="2">
        <el-button
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:houseVillage:add']"
          >新增</el-button
        >
      </el-col>
      <!-- <el-col :span="9">
      </el-col>
      <el-col :span="11">
      </el-col> -->
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        label-width="68px"
        class="el-form-search"
      >
        <el-form-item label="取值方式" prop="type" class="el-form-search-item">
          <el-select v-model="queryParams.type" placeholder="请选择" clearable>
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属项" prop="upField" class="el-form-search-item">
          <el-select
            v-model="queryParams.upField"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="item in options1"
              :key="item.value"
              :label="item.label"
              :value="item.label"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="名称" prop="field" class="el-form-search-item">
          <el-input
            v-model="queryParams.field"
            placeholder="请输入名称"
            clearable
            size="mini"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
    </el-row>

    <el-table
      :height="tableHeight"
      v-loading="loading"
      :data="houseVillageList"
    >
      <el-table-column label="#" type="index" width="50" align="center">
        <template scope="scope">
          <span>{{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="表头名称" align="center" prop="field" />
      <el-table-column label="取值方式" align="center" prop="type">
        <template slot-scope="scope">
          <el-tag
            :type="
              scope.row.type == 1
                ? 'success'
                : scope.row.type == 2
                ? 'primary'
                : scope.row.type == 3
                ? 'info'
                : ''
            "
            >{{
              scope.row.type == 1
                ? "数据导入"
                : scope.row.type == 2
                ? "规则计算"
                : scope.row.type == 3
                ? "系统设置"
                : "未知"
            }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column label="所属项" align="center" prop="upField">
        <template slot-scope="scope">
          <span>{{ scope.row.upField == "" ? "无" : scope.row.upField }}</span>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="sort" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.type == 2"
            size="mini"
            type="text"
            icon="el-icon-plus"
            @click="gohandleUpdate(scope.row)"
            >设置规则</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改房源小区对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="700px"
      append-to-body
      close-on-click-modal
      v-dialogDrag
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="表头名称">
              <el-input v-model="form.field" placeholder="请输入名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="取值方式" prop="type">
              <el-select v-model="form.type" placeholder="请选择">
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="排序" prop="sort">
              <el-input-number
                v-model="num"
                @change="handleChange"
                :min="1"
                :max="999"
                :step="5"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="上级表头" prop="upField">
              <el-select v-model="form.upField" placeholder="请选择" clearable>
                <el-option
                  v-for="item in options1"
                  :key="item.value"
                  :label="item.label"
                  :value="item.label"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="关联职务" prop="userType">
              <el-select v-model="form.userType" placeholder="请选择" clearable>
                <el-option
                  v-for="item in options2"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关联部门" prop="deptList">
              <el-select
                v-model="form.deptList"
                placeholder="请选择"
                clearable
                multiple
              >
                <el-option
                  v-for="item in options3"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listHouseVillage,
  getHouseVillage,
  delHouseVillage,
  addHouseVillage,
  updateHouseVillage,
  listHeaders,
  deptList,
} from "@/api/house/houseVillage";

export default {
  name: "HouseVillage",
  dicts: ["sys_common_status"],
  data() {
    return {
      // 取值方式
      options: [
        {
          value: "1",
          label: "数据导入",
        },
        {
          value: "2",
          label: "规则计算",
        },
        {
          value: "3",
          label: "系统设置",
        },
      ],
      options1: [],
      options2: [
        {
          value: "1",
          label: "全部",
        },
        {
          value: "2",
          label: "主管",
        },
        {
          value: "3",
          label: "业务员",
        },
      ],
      options3: [],
      // 表格高度
      tableHeight: document.documentElement.clientHeight - 280,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 房源小区表格数据
      houseVillageList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        name: "",
        upField: "",
        type: "",
      },
      // 表单参数
      num: 1,
      form: {
        sort: 1,
      },
      // 表单校验
      rules: {
        field: [{ required: true, message: "表头名不能为空", trigger: "blur" }],
        type: [
          { required: true, message: "取值方式不能为空", trigger: "blur" },
        ],
        sort: [{ required: true, message: "排序不能为空", trigger: "blur" }],
      },
    };
  },
  created() {
    this.getHeaderList();
    this.getList();
    this.getDeptList();
  },
  methods: {
    /** 查询项目组列表 */
    getDeptList() {
      this.loading = true;
      deptList().then((response) => {
        this.options3 = response.data.map((item) => {
          return { label: item.deptName, value: item.deptId };
        });
      });
    },

    /** 查询上级表头列表 */
    getHeaderList() {
      this.loading = true;
      listHeaders().then((response) => {
        console.log(response);
        // this.houseVillageList = response.rows;
        // this.total = response.total;
        // this.loading = false;
        this.options1 = response.data.map((item) => {
          return {
            label: item.dictLabel,
            value: item.dictSort,
          };
        });
      });
    },

    /** 查询房源小区列表 */
    getList() {
      this.loading = true;
      listHouseVillage(this.queryParams).then((response) => {
        console.log(response);
        this.houseVillageList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.num = 1;
      this.form = {
        id: null,
        upField: null,
        sort: 1,
        type: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    // handleSelectionChange(selection) {
    //   this.ids = selection.map((item) => item.id);
    //   this.single = selection.length !== 1;
    //   this.multiple = !selection.length;
    // },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加表头";
    },
    // 修改按钮
    gohandleUpdate(row) {
      // 跳转
      console.log(row);
      this.$router.push({
        path: "/house/Salaryrules",
        query: { id: row.id, field: row.field },
      });
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id;
      getHouseVillage(id).then((response) => {
        console.log(response);
        this.form = response.data;
        this.num = response.data.sort;
        // 字符串切成数组
        this.form.deptList = this.form.deptList
          ? response.data.deptList.split(",")
          : [];
        this.form.deptList = this.form.deptList.map(Number);
        this.open = true;
        this.title = "修改表头";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        // 将deptList转为字符串
        if (this.form.deptList) {
          this.form.deptList = this.form.deptList.join(",");
        }
        if (valid) {
          if (this.form.id != null) {
            updateHouseVillage(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addHouseVillage(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id;
      this.$modal
        .confirm("是否确认删除记录？")
        .then(function () {
          return delHouseVillage(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/houseVillage/export",
        {
          ...this.queryParams,
        },
        `houseVillage_${new Date().getTime()}.xlsx`
      );
    },

    // 计数器
    handleChange(e) {
      this.form.sort = e;
    },
  },
};
</script>
