{"name": "fuse.js", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://kiro.me"}, "main": "./dist/fuse.common.js", "module": "./dist/fuse.esm.js", "unpkg": "./dist/fuse.js", "jsdelivr": "./dist/fuse.js", "typings": "./dist/fuse.d.ts", "files": ["dist"], "version": "6.4.3", "description": "Lightweight fuzzy-search", "license": "Apache-2.0", "repository": {"type": "git", "url": "https://github.com/krisk/Fuse.git"}, "homepage": "http://fusejs.io", "keywords": ["fuzzy", "search", "bitap"], "scripts": {"dev": "rollup -w -c scripts/configs.js --environment TARGET:umd-dev-full", "dev:cjs": "rollup -w -c scripts/configs.js --environment TARGET:commonjs-full", "dev:esm": "rollup -w -c scripts/configs.js --environment TARGET:esm-dev-full", "build": "rm -r dist && mkdir dist && node ./scripts/build.main.js", "test": "jest", "lint": "eslint src scripts test", "release": "standard-version -a", "docs:bump": "node ./scripts/bump-docs.js", "docs:dev": "vuepress dev docs", "docs:build": "vuepress build docs", "docs:release": "./scripts/deploy-docs.sh"}, "standard-version": {"scripts": {"postbump": "yarn build && yarn lint && yarn test 2>/dev/null", "precommit": "git add dist/*.js dist/*.ts"}}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.3.4", "@babel/plugin-proposal-object-rest-spread": "7.9.0", "@babel/preset-env": "7.3.4", "@babel/preset-typescript": "7.9.0", "@commitlint/cli": "^9.0.1", "@commitlint/config-conventional": "^9.0.1", "@rollup/plugin-node-resolve": "^7.1.1", "@rollup/plugin-replace": "^2.3.1", "@types/jest": "25.1.4", "@vuepress/plugin-google-analytics": "^1.4.0", "@vuepress/plugin-register-components": "^1.5.2", "babel-eslint": "^10.1.0", "babel-loader": "^8.0.5", "codemirror": "5.52.2", "eslint": "6.8.0", "eslint-config-prettier": "6.10.1", "eslint-plugin-vue": "7.0.0-alpha.0", "faker": "4.1.0", "husky": "^4.2.5", "jest": "^25.1.0", "prettier": "2.0.2", "replace-in-file": "^6.1.0", "rimraf": "3.0.2", "rollup": "2.1.0", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-copy": "3.3.0", "standard-version": "^8.0.0", "terser-webpack-plugin": "2.3.5", "typescript": "^3.8.3", "vue-codemirror": "^4.0.6", "vue-eslint-parser": "^7.0.0", "vuepress": "^1.4.0", "vuepress-plugin-element-tabs": "^0.2.8", "vuepress-plugin-smooth-scroll": "^0.0.9", "vuepress-plugin-social-share": "^0.2.1", "webpack": "^4.42.0", "webpack-cli": "^3.3.11"}, "engines": {"node": ">=10"}, "dependencies": {}}