import request from "@/utils/request";

// 查询愿望列表
export function getWishList(query) {
  return request({
    url: "/business/workWish/list",
    method: "get",
    params: query,
  });
}

// 导出愿望列表
export function exportWish(data) {
  return request({
    url: "/business/workWish/export",
    method: "post",
    responseType: "blob",
    data: data,
  });
}

// 新增愿望
export function addWish(data) {
  return request({
    url: "/business/workWish/add",
    method: "post",
    data: data,
  });
}

// 修改愿望
export function updateWish(data) {
  return request({
    url: "/business/workWish/update",
    method: "post",
    data: data,
  });
}

// 删除愿望
export function deleteWish(id) {
  return request({
    url: "/business/workWish/delete/" + id,
    method: "delete",
  });
}
