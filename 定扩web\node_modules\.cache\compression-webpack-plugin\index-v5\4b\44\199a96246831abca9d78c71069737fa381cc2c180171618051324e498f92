
4ec5122e8e544ec3c0833ece93d26b3ca3910f6e	{"key":"{\"nodeVersion\":\"v16.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"0.e9548bd97ed62efd0c32.hot-update.js\",\"contentHash\":\"bcbffa10bf6a62a20e5423c13e8f7aab\"}","integrity":"sha512-urFSPLklyxQMiaOEsLIUHLt4aJ3QRckWhlj/GrATDITCHv6Z0wDnmnMm9TI1MO+qqipuTrrl+WrLX2gz1T+HUg==","time":1753843757446,"size":44893}