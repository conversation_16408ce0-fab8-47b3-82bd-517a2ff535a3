{"name": "jsencrypt", "version": "3.2.1", "description": "A Javascript library to perform OpenSSL RSA Encryption, Decryption, and Key Generation.", "main": "bin/jsencrypt.js", "module": "lib/index.js", "types": "lib/index.d.ts", "dependencies": {}, "devDependencies": {"typescript": "^4.2.4", "webpack": "^5.35.1", "webpack-cli": "^4.6.0"}, "files": ["bin", "lib"], "scripts": {"build:dev": "tsc && tsc --project tsconfig-def.json && webpack", "build:prod": "tsc && tsc --project tsconfig-def.json && webpack --config webpack.prod.js", "build": "npm run build:dev && npm run build:prod", "serve": "bundle exec jekyll server --config _config.build.yml"}, "author": "<PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/travist"}, {"name": "Antonio", "url": "https://github.com/zoloft"}, {"name": "<PERSON>", "url": "https://github.com/jmgaya"}], "homepage": "http://www.travistidwell.com/jsencrypt", "repository": {"type": "git", "url": "git://github.com/travist/jsencrypt.git"}, "bugs": {"url": "http://github.com/travist/jsencrypt/issues"}, "license": "MIT"}