
8d741acf89c52c5430fc9aebfbdd867aca69103c	{"key":"{\"nodeVersion\":\"v16.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"8785019047ea4281b63f6c2427f57117\"}","integrity":"sha512-WYQmO7rjQXtUHkppnyemOsw8h6ikIPK9JZmXR3jQLiDEJnbL6Q/z2L6iANiW/oqmQNOKfrHlSPIDSaDnfDK8EA==","time":1753843831490,"size":3428543}