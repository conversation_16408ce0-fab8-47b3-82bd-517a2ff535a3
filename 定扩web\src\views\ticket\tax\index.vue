<template>
  <div class="tax-container">
    <!-- 搜索表单 -->
    <el-form :inline="true" :model="searchForm" class="search-form">
      <el-form-item label="打款时间">
        <el-date-picker
          v-model="searchForm.addTime"
          type="date"
          placeholder="选择打款时间"
          value-format="yyyy-MM-dd"
          clearable
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区 -->
    <div class="operation-area">
      <el-button type="primary" plain @click="handleAdd">新增打款</el-button>
    </div>

    <!-- 汇总信息 -->
    <el-row :gutter="20" class="summary-info">
      <el-col :span="6">
        <div class="summary-item">
          <span class="label">累计打款：</span>
          <span class="value">{{ summaryData.totalPay || 0 }}元</span>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="summary-item">
          <span class="label">回款金额：</span>
          <span class="value">{{ summaryData.returnPay || 0 }}元</span>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="summary-item">
          <span class="label">未回款金额：</span>
          <span class="value">{{ summaryData.unreturnPay || 0 }}元</span>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="summary-item">
          <span class="label">剩余金额：</span>
          <span class="value">{{ summaryData.remainPay || 0 }}元</span>
        </div>
      </el-col>
    </el-row>

    <!-- 表格 -->
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column prop="addTime" label="打款时间" align="center" />
      <el-table-column prop="money" label="入账金额（元）" align="center" />
      <el-table-column prop="remark" label="备注" align="center" />
      <el-table-column label="操作" width="150" align="center">
        <template #default="scope">
          <el-button type="text" size="small" @click="handleEdit(scope.row)"
            >编辑</el-button
          >
          <el-button type="text" size="small" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加分页组件 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
        :current-page="pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>

    <!-- 新增/编辑弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="500px">
      <el-form
        :model="formData"
        :rules="rules"
        ref="formRef"
        label-width="100px"
      >
        <el-form-item label="打款时间" prop="addTime">
          <el-date-picker
            v-model="formData.addTime"
            type="date"
            placeholder="选择打款时间"
            value-format="yyyy-MM-dd"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="入账金额" prop="money">
          <el-input-number
            v-model="formData.money"
            :min="0"
            :precision="2"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="formData.remark" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  getMoneyList,
  addMoney,
  updateMoney,
  deleteMoney,
} from "@/api/ticket/money";

export default {
  name: "TaxManagement",
  data() {
    return {
      // 搜索表单数据
      searchForm: {
        addTime: "",
      },
      // 表格数据
      tableData: [],
      // 汇总数据
      summaryData: {
        totalPay: 0,
        returnPay: 0,
        unreturnPay: 0,
        remainPay: 0,
      },
      // 弹窗显示状态
      dialogVisible: false,
      // 弹窗标题
      dialogTitle: "新增打款",
      // 表单数据
      formData: {
        addTime: "",
        money: 0,
        remark: "",
      },
      // 表单校验规则
      rules: {
        addTime: [
          { required: true, message: "请选择打款时间", trigger: "change" },
        ],
        money: [{ required: true, message: "请输入入账金额", trigger: "blur" }],
        remark: [{ required: true, message: "请输入备注", trigger: "blur" }],
      },
      // 添加分页相关数据
      pageNum: 1,
      pageSize: 10,
      total: 0,
    };
  },
  methods: {
    // 获取表格数据
    async getTableData() {
      try {
        const params = {
          ...this.searchForm,
          pageNum: this.pageNum,
          pageSize: this.pageSize,
        };
        const res = await getMoneyList(params);
        if (res.code === 200) {
          this.tableData = res.rows;
          this.summaryData = res.data || this.summaryData;
          this.total = res.total;
        } else {
          this.$message.error(res.msg || "获取数据失败");
        }
      } catch (error) {
        console.error("获取表格数据失败:", error);
        this.$message.error("获取数据失败");
      }
    },

    // 搜索
    handleSearch() {
      this.pageNum = 1; // 搜索时重置为第一页
      this.getTableData();
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        addTime: "",
      };
      this.pageNum = 1; // 重置分页
      this.getTableData();
    },

    // 新增
    handleAdd() {
      this.dialogTitle = "新增打款";
      this.dialogVisible = true;
      this.formData = {
        addTime: "",
        money: 0,
        remark: "",
      };
    },

    // 编辑
    handleEdit(row) {
      this.dialogTitle = "编辑打款";
      this.dialogVisible = true;
      this.formData = { ...row };
    },

    // 删除
    handleDelete(row) {
      this.$confirm("确认删除该记录吗？", "提示", {
        type: "warning",
      })
        .then(async () => {
          try {
            const res = await deleteMoney(row.id);
            if (res.code === 200) {
              this.$message.success("删除成功");
              this.getTableData();
            } else {
              this.$message.error(res.msg || "删除失败");
            }
          } catch (error) {
            console.error("删除失败:", error);
            this.$message.error("删除失败");
          }
        })
        .catch(() => {});
    },

    // 提交表单
    handleSubmit() {
      this.$refs.formRef.validate(async (valid) => {
        if (valid) {
          try {
            const isEdit = this.formData.id !== undefined;
            const res = await (isEdit ? updateMoney : addMoney)(this.formData);

            if (res.code === 200) {
              this.$message.success(isEdit ? "修改成功" : "添加成功");
              this.dialogVisible = false;
              this.getTableData();
            } else {
              this.$message.error(
                res.msg || (isEdit ? "修改失败" : "添加失败")
              );
            }
          } catch (error) {
            console.error(isEdit ? "修改失败:" : "添加失败:", error);
            this.$message.error(isEdit ? "修改失败" : "添加失败");
          }
        }
      });
    },

    // 添加处理分页变化的方法
    handlePageChange(page) {
      this.pageNum = page;
      this.getTableData();
    },

    // 添加处理每页显示条数变化的方法
    handleSizeChange(size) {
      this.pageSize = size;
      this.pageNum = 1; // 重置为第一页
      this.getTableData();
    },
  },
  created() {
    // 页面创建时获取表格数据
    this.getTableData();
  },
};
</script>

<style lang="scss" scoped>
.tax-container {
  padding: 20px;

  .search-form {
    margin-bottom: 16px;
  }

  .operation-area {
    margin-bottom: 16px;
  }

  .summary-info {
    margin-bottom: 20px;

    .summary-item {
      background: #f5f7fa;
      padding: 15px;
      border-radius: 4px;

      .label {
        color: #606266;
      }

      .value {
        color: #409eff;
        font-weight: bold;
        margin-left: 8px;
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
