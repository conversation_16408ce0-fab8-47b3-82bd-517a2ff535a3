import request from "@/utils/request";

// 查询M3核销统计列表
export function getMonthThirdList(query) {
  return request({
    url: "/business/monthThird/list",
    method: "get",
    params: query,
  });
}

// 导出M3核销统计模板
export function exportMonthThirdModel() {
  return request({
    url: "/business/monthThird/exportModel",
    method: "post",
    responseType: "blob",
  });
}

// 导入M3核销统计数据
export function importMonthThirdData(data) {
  return request({
    url: "/business/monthThird/upload",
    method: "post",
    data: data,
  });
}
