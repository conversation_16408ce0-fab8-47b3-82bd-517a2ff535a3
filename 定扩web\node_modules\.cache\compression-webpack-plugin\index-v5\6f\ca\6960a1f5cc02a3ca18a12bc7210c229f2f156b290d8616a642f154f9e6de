
f037b5fe174eb3a6a96753e2cb01251a05deb2fe	{"key":"{\"nodeVersion\":\"v16.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"df733ba76ae89b433af5ceffbb5e1aee\"}","integrity":"sha512-yke5ULkSp178DFNss3OF8HcDG1lruAI4RibEYdJtvj+8AUwPX4XymSjxCyvJCecGcCT4wY3IZDE3I1T6o3K2RQ==","time":1753843745781,"size":11431536}