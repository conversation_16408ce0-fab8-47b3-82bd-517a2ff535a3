import request from "@/utils/request";

// 查询列表
export function listHouseVillage(query) {
  return request({
    url: "/system/time/list",
    method: "get",
    params: query,
  });
}

// 查询房源小区详细
export function getHouseVillage(id) {
  return request({
    url: "/system/time/" + id,
    method: "get",
  });
}

// 新增表头列表
export function addHouseVillage(data) {
  return request({
    url: "/system/time",
    method: "post",
    data: data,
  });
}

// 修改表头
export function updateHouseVillage(data) {
  return request({
    url: "/system/time",
    method: "put",
    data: data,
  });
}

// 删除房源小区
export function delHouseVillage(id) {
  return request({
    url: "/system/time/" + id,
    method: "delete",
  });
}
