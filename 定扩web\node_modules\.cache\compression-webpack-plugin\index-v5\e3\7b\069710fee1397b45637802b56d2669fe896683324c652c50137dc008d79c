
ca0941d447baa9dc888c77428320ad55279c8533	{"key":"{\"nodeVersion\":\"v16.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"2403edef27937e1ce49d06129eb395ee\"}","integrity":"sha512-9ws+JEtoZOS10nqpqh7EGYif2CzzdpQYZ2IJpFB8IFx1drInapV/McubEYXBB1tpeJGjgdk6dkMZ4GyuLoig9g==","time":1753843757729,"size":3428325}