{"name": "vue-meta", "version": "2.4.0", "description": "Manage HTML metadata in Vue.js components with ssr support", "keywords": ["attribute", "google", "head", "helmet", "info", "metadata", "meta", "seo", "server", "ssr", "title", "universal", "vue"], "homepage": "https://github.com/nuxt/vue-meta", "bugs": "https://github.com/nuxt/vue-meta/issues", "repository": {"type": "git", "url": "**************/nuxt/vue-meta.git"}, "license": "MIT", "contributors": [{"name": "<PERSON> (@declandewet)"}, {"name": "<PERSON><PERSON><PERSON> (@Atinux)"}, {"name": "Pim (@pimlie)"}], "files": ["dist", "types/*.d.ts"], "main": "dist/vue-meta.common.js", "web": "dist/vue-meta.js", "module": "dist/vue-meta.esm.js", "typings": "types/index.d.ts", "scripts": {"build": "rimraf dist && rollup -c scripts/rollup.config.js", "coverage": "codecov", "dev": "cd examples && yarn dev && cd ..", "docs": "vuepress dev --host 0.0.0.0 --port 3000 docs", "docs:build": "vuepress build docs", "lint": "eslint src test", "prerelease": "git checkout master && git pull -r", "release": "yarn lint && yarn test && standard-version", "test": "yarn test:unit && yarn test:e2e-ssr && yarn test:e2e-browser", "test:e2e-ssr": "jest test/e2e/ssr", "test:e2e-browser": "jest test/e2e/browser", "test:unit": "jest test/unit", "test:types": "tsc -p types/test"}, "dependencies": {"deepmerge": "^4.2.2"}, "devDependencies": {"@babel/cli": "^7.10.1", "@babel/core": "^7.10.2", "@babel/node": "^7.10.1", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/preset-env": "^7.10.2", "@nuxtjs/eslint-config": "^3.0.0", "@vue/server-test-utils": "^1.0.3", "@vue/test-utils": "^1.0.3", "@vuepress/plugin-google-analytics": "^1.5.1", "@vuepress/plugin-pwa": "^1.5.1", "babel-core": "^7.0.0-bridge", "babel-eslint": "^10.1.0", "babel-jest": "^26.0.1", "babel-loader": "^8.1.0", "babel-plugin-dynamic-import-node": "^2.3.3", "browserstack-local": "^1.4.5", "chromedriver": "^83.0.0", "codecov": "^3.7.0", "copy-webpack-plugin": "^6.0.2", "eslint": "^7.2.0", "eslint-config-standard": "^14.1.1", "eslint-plugin-import": "^2.21.2", "eslint-plugin-jest": "^23.13.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "eslint-plugin-vue": "^6.2.2", "esm": "^3.2.25", "fs-extra": "^9.0.1", "geckodriver": "^1.19.1", "get-port": "^5.1.1", "hable": "3.0.0", "is-wsl": "^2.2.0", "jest": "^26.0.1", "jest-environment-jsdom": "^26.0.1", "jest-environment-jsdom-global": "^2.0.2", "jsdom": "^16.2.2", "lodash": "^4.17.15", "node-env-file": "^0.1.8", "puppeteer-core": "^3.3.0", "rimraf": "^3.0.2", "rollup": "^2.15.0", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-json": "^4.0.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-terser": "^6.1.0", "selenium-webdriver": "^4.0.0-alpha.7", "standard-version": "^8.0.0", "tib": "^0.7.4", "typescript": "^3.9.5", "vue": "^2.6.11", "vue-jest": "^3.0.5", "vue-loader": "^15.9.2", "vue-router": "^3.3.2", "vue-server-renderer": "^2.6.11", "vue-template-compiler": "^2.6.11", "vuepress": "^1.5.1", "vuepress-theme-vue": "^1.1.0", "webpack": "^4.43.0"}}