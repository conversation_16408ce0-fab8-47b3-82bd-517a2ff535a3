
533c51c1fe6789a24d597793d0e0cd62ca21743d	{"key":"{\"nodeVersion\":\"v16.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"0.e04a28b0ad3a131355ac.hot-update.js\",\"contentHash\":\"b3a1b8cda3021806c14d575415129411\"}","integrity":"sha512-Zucvs/1XdMviD4c1bfOsxJHLd/u7uc19K6ExRCr3a7wxrveplYwzG/tzumwRZ92GMWU+G8JCfEj/iiIYI5JD3w==","time":1753843716168,"size":77149}