import request from "@/utils/request";

// 查询银行卡列表
export function getWorkBankList(query) {
  return request({
    url: "/business/workBank/list",
    method: "get",
    params: query,
  });
}

// 导出模板
export function exportTemplate() {
  return request({
    url: "/business/workBank/export",
    method: "post",
    responseType: "blob",
  });
}

// 导入银行卡数据
export function importWorkBank(file) {
  const formData = new FormData();
  formData.append("file", file);
  return request({
    url: "/business/workBank/upload",
    method: "post",
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}
