import request from "@/utils/request";

// 查询内容管理列表
export function listArticle(data) {
  return request({
    url:
      "/system/user/salesmanDataList?pageNum=" +
      data.pageNum +
      "&pageSize=" +
      data.pageSize,
    method: "post",
    data,
  });
}

// 查询内容管理表头
export function listArticles(data) {
  return request({
    url: "/system/user/salesmanDataTable",
    method: "post",
    data,
  });
}

// 查询内容管理详细
export function getArticle(id) {
  return request({
    url: "/system/article/" + id,
    method: "get",
  });
}

// 新增内容管理
export function addArticle(data) {
  return request({
    url: "/system/article",
    method: "post",
    data: data,
  });
}

// 修改内容管理
export function updateArticle(data) {
  return request({
    url: "/system/article",
    method: "put",
    data: data,
  });
}

// 删除内容管理
export function delArticle(id) {
  return request({
    url: "/system/article/" + id,
    method: "delete",
  });
}

// 导出
export function getexl(data) {
  return request({
    url: "/system/user/workDataExport",
    method: "post",
    data,
  });
}
