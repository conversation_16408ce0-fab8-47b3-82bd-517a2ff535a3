
67480cfffb6f9f50db931068165aff1c97bdb1ec	{"key":"{\"nodeVersion\":\"v16.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"714dbf8d91e0f6a8a221a438917601e3\"}","integrity":"sha512-h0luR3EvIhI8Ha1yzylh8IKVG8/6jEQOucvL6z5l4dpVPrSVhssFe3cTNqGZ33TnW5zy7lG8ZrSd/Q11K3fTyw==","time":1753843831908,"size":11432209}