{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\定阔\\dingkuao\\定扩web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\定阔\\dingkuao\\定扩web\\src\\views\\mThree\\curls\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\定阔\\dingkuao\\定扩web\\src\\views\\mThree\\curls\\index.vue", "mtime": 1753843713643}, {"path": "C:\\Users\\<USER>\\Desktop\\定阔\\dingkuao\\定扩web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1713250439495}, {"path": "C:\\Users\\<USER>\\Desktop\\定阔\\dingkuao\\定扩web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1713250440428}, {"path": "C:\\Users\\<USER>\\Desktop\\定阔\\dingkuao\\定扩web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1713250439495}, {"path": "C:\\Users\\<USER>\\Desktop\\定阔\\dingkuao\\定扩web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1713250440827}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgYXhpb3MgZnJvbSAiYXhpb3MiOw0KaW1wb3J0IHsgZ2V0VG9rZW4gfSBmcm9tICJAL3V0aWxzL2F1dGgiOw0KaW1wb3J0IHsgZ2V0Q3VybHNMaXN0LCBzZW5kQ2FyZCwgc2VuZENhcmRMaXN0IH0gZnJvbSAiQC9hcGkvc3lzdGVtL2N1cmxzIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiQ3VybHMiLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDpga7nvanlsYINCiAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICAvLyDpgInkuK3mlbDnu4QNCiAgICAgIGlkczogW10sDQogICAgICAvLyDmgLvmnaHmlbANCiAgICAgIHRvdGFsOiAwLA0KICAgICAgLy8g6KGo5qC85pWw5o2uDQogICAgICB0YWJsZURhdGE6IFtdLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBkYXRlUmFuZ2U6IFtdLA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDIwLA0KICAgICAgICBkZXB0TmFtZTogdW5kZWZpbmVkLA0KICAgICAgICBhcHBseU5hbWU6IHVuZGVmaW5lZCwNCiAgICAgICAgcmVkQ2FyZDogdW5kZWZpbmVkLA0KICAgICAgICBzdGFydFRpbWU6IHVuZGVmaW5lZCwNCiAgICAgICAgZW5kVGltZTogdW5kZWZpbmVkLA0KICAgICAgfSwNCiAgICAgIC8vIOWvvOWHuuW8ueahhuaYvuekuueKtuaAgQ0KICAgICAgZXhwb3J0RGlhbG9nOiBmYWxzZSwNCiAgICAgIC8vIOWvvOWHuuihqOWNleWPguaVsA0KICAgICAgZXhwb3J0Rm9ybTogew0KICAgICAgICBkYXRlUmFuZ2U6IFtdLA0KICAgICAgICBleHBvcnRUeXBlOiB1bmRlZmluZWQsDQogICAgICB9LA0KICAgICAgLy8g5a+85Ye66KGo5Y2V5qCh6aqM6KeE5YiZDQogICAgICBleHBvcnRSdWxlczogew0KICAgICAgICBkYXRlUmFuZ2U6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup5pe26Ze06IyD5Zu0IiwgdHJpZ2dlcjogImNoYW5nZSIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgZXhwb3J0VHlwZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6nlr7zlh7rnsbvlnosiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9LA0KICAgICAgICBdLA0KICAgICAgfSwNCiAgICAgIHRhYmxlSGVpZ2h0OiA2NTAsDQogICAgICBzZWxlY3RlZFJvd3M6IFtdLCAvLyDmlrDlop7pgInkuK3ooYzmlbDnu4QNCiAgICAgIC8vIOWPkemAgeWNoeWIuOWIl+ihqOW8ueahhuaYvuekuueKtuaAgQ0KICAgICAgc2VuZENhcmRMaXN0RGlhbG9nOiBmYWxzZSwNCiAgICAgIC8vIOWPkemAgeWNoeWIuOWIl+ihqOihqOWNleWPguaVsA0KICAgICAgc2VuZENhcmRMaXN0Rm9ybTogew0KICAgICAgICBzdG9ja0lkOiAiIiwNCiAgICAgIH0sDQogICAgICAvLyDlj5HpgIHljaHliLjliJfooajooajljZXmoKHpqozop4TliJkNCiAgICAgIHNlbmRDYXJkTGlzdFJ1bGVzOiB7DQogICAgICAgIHN0b2NrSWQ6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWl5om55qyh56CBIiwgdHJpZ2dlcjogImJsdXIiIH1dLA0KICAgICAgfSwNCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOafpeivouWIl+ihqCAqLw0KICAgIGFzeW5jIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KDQogICAgICAvLyDlpITnkIbml7bpl7Tlj4LmlbANCiAgICAgIGlmICh0aGlzLmRhdGVSYW5nZSAmJiB0aGlzLmRhdGVSYW5nZS5sZW5ndGggPT09IDIpIHsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zdGFydFRpbWUgPSB0aGlzLmRhdGVSYW5nZVswXTsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5lbmRUaW1lID0gdGhpcy5kYXRlUmFuZ2VbMV07DQogICAgICB9DQoNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGdldEN1cmxzTGlzdCh0aGlzLnF1ZXJ5UGFyYW1zKTsNCiAgICAgICAgdGhpcy50YWJsZURhdGEgPSByZXMucm93czsNCiAgICAgICAgdGhpcy50b3RhbCA9IHJlcy50b3RhbDsNCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICB0aGlzLmRhdGVSYW5nZSA9IFtdOw0KICAgICAgdGhpcy5zZWxlY3RlZFJvd3MgPSBbXTsNCiAgICAgIGlmICh0aGlzLiRyZWZzLnRhYmxlKSB7DQogICAgICAgIHRoaXMuJHJlZnMudGFibGUuY2xlYXJTZWxlY3Rpb24oKTsNCiAgICAgIH0NCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICB9LA0KICAgIC8qKiDooaXlj5HmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVFZGl0KHJvdykgew0KICAgICAgdGhpcy4kbW9kYWwNCiAgICAgICAgLmNvbmZpcm0oYOehruiupOimgeWvuSIke3Jvdy5uaWNrTmFtZX0i6L+b6KGM6KGl5Y+R5pON5L2c5ZCX77yfYCkNCiAgICAgICAgLnRoZW4oYXN5bmMgKCkgPT4gew0KICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5sb2FkaW5nKCLmraPlnKjooaXlj5HkuK3vvIzor7fnqI3lgJkuLi4iKTsNCiAgICAgICAgICAgIC8vIOiwg+eUqOihpeWPkeaOpeWPo++8jOWNleS4quihpeWPkeaXtuS8oOWFpeWNleS4qmlk55qE5pWw57uEDQogICAgICAgICAgICBhd2FpdCBzZW5kQ2FyZCh7IGxpc3Q6IFtyb3cub3BlbmlkXSB9KTsNCiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuihpeWPkeaIkOWKnyIpOw0KICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIuihpeWPkeWksei0pToiLCBlcnJvcik7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6KGl5Y+R5aSx6LSl77yM6K+36YeN6K+VIik7DQogICAgICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgICAgIHRoaXMuJG1vZGFsLmNsb3NlTG9hZGluZygpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIC8qKiDooajmoLzlpJrpgInmoYbpgInkuK3mlbDmja4gKi8NCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLnNlbGVjdGVkUm93cyA9IHNlbGVjdGlvbi5maWx0ZXIoKHJvdykgPT4gIXJvdy5yZWRDYXJkKTsNCiAgICB9LA0KICAgIC8qKiDkuIDplK7ooaXlj5HmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVSZXNlbmQoKSB7DQogICAgICBpZiAoIXRoaXMuc2VsZWN0ZWRSb3dzLmxlbmd0aCkgew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6K+35YWI6YCJ5oup6KaB6KGl5Y+R55qE5a6i5oi3Iik7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgY29uc3QgbmFtZXMgPSB0aGlzLnNlbGVjdGVkUm93cy5tYXAoKGl0ZW0pID0+IGl0ZW0ubmlja05hbWUpLmpvaW4oIuOAgSIpOw0KICAgICAgdGhpcy4kbW9kYWwNCiAgICAgICAgLmNvbmZpcm0oYOehruiupOimgeWvuSIke25hbWVzfSLov5vooYzooaXlj5Hmk43kvZzlkJfvvJ9gKQ0KICAgICAgICAudGhlbihhc3luYyAoKSA9PiB7DQogICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgIHRoaXMuJG1vZGFsLmxvYWRpbmcoIuato+WcqOihpeWPkeS4re+8jOivt+eojeWAmS4uLiIpOw0KICAgICAgICAgICAgLy8g6LCD55So6KGl5Y+R5o6l5Y+j77yM5Lyg5YWl6YCJ5Lit6KGM55qEb3Blbmlk5pWw57uEDQogICAgICAgICAgICBjb25zdCBvcGVuaWRzID0gdGhpcy5zZWxlY3RlZFJvd3MubWFwKChpdGVtKSA9PiBpdGVtLm9wZW5pZCk7DQogICAgICAgICAgICBhd2FpdCBzZW5kQ2FyZCh7IGxpc3Q6IG9wZW5pZHMgfSk7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLooaXlj5HmiJDlip8iKTsNCiAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgICAgICBjb25zb2xlLmVycm9yKCLooaXlj5HlpLHotKU6IiwgZXJyb3IpOw0KICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuihpeWPkeWksei0pe+8jOivt+mHjeivlSIpOw0KICAgICAgICAgIH0gZmluYWxseSB7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5jbG9zZUxvYWRpbmcoKTsNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog5pi+56S65a+85Ye65by55qGGICovDQogICAgaGFuZGxlRXhwb3J0RGlhbG9nKCkgew0KICAgICAgdGhpcy5leHBvcnREaWFsb2cgPSB0cnVlOw0KICAgICAgdGhpcy5leHBvcnRGb3JtID0gew0KICAgICAgICBkYXRlUmFuZ2U6IFtdLA0KICAgICAgICBleHBvcnRUeXBlOiB1bmRlZmluZWQsDQogICAgICB9Ow0KICAgIH0sDQogICAgLyoqIOehruiupOWvvOWHuuaTjeS9nCAqLw0KICAgIGNvbmZpcm1FeHBvcnQoKSB7DQogICAgICB0aGlzLiRyZWZzLmV4cG9ydEZvcm0udmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgICAgIHN0YXJ0VGltZTogdGhpcy5leHBvcnRGb3JtLmRhdGVSYW5nZVswXSwNCiAgICAgICAgICAgIGVuZFRpbWU6IHRoaXMuZXhwb3J0Rm9ybS5kYXRlUmFuZ2VbMV0sDQogICAgICAgICAgfTsNCg0KICAgICAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oIuaYr+WQpuehruiupOWvvOWHuuaVsOaNru+8nyIpLnRoZW4oKCkgPT4gew0KICAgICAgICAgICAgdGhpcy4kbW9kYWwubG9hZGluZygi5q2j5Zyo5a+85Ye65pWw5o2u77yM6K+356iN5YCZIik7DQoNCiAgICAgICAgICAgIC8vIOagueaNrumAieaLqeeahOWvvOWHuuexu+Wei+ehruWumuaOpeWPo+WcsOWdgA0KICAgICAgICAgICAgY29uc3QgdXJsTWFwID0gew0KICAgICAgICAgICAgICBjdXN0b21lcjogIi9zeXN0ZW0vY3VybHMvZXhwb3J0IiwNCiAgICAgICAgICAgICAgc3RhZmY6ICIvc3lzdGVtL2N1cmxzL2V4cG9ydFN0YWZmIiwNCiAgICAgICAgICAgICAgZGVwdDogIi9zeXN0ZW0vY3VybHMvZXhwb3J0RGVwdCIsDQogICAgICAgICAgICB9Ow0KDQogICAgICAgICAgICAvLyDmoLnmja7pgInmi6nnmoTlr7zlh7rnsbvlnovnoa7lrprmlofku7blkI0NCiAgICAgICAgICAgIGNvbnN0IGZpbGVOYW1lTWFwID0gew0KICAgICAgICAgICAgICBjdXN0b21lcjogYCR7cGFyYW1zLnN0YXJ0VGltZX0tJHtwYXJhbXMuZW5kVGltZX3lrqLmiLfpooblj5bmmI7nu4ZgLA0KICAgICAgICAgICAgICBzdGFmZjogYCR7cGFyYW1zLnN0YXJ0VGltZX0tJHtwYXJhbXMuZW5kVGltZX3lkZjlt6Xlj5HljbfmmI7nu4ZgLA0KICAgICAgICAgICAgICBkZXB0OiBgJHtwYXJhbXMuc3RhcnRUaW1lfS0ke3BhcmFtcy5lbmRUaW1lfemhueebrue7hOWPkeWNt+aYjue7hmAsDQogICAgICAgICAgICB9Ow0KDQogICAgICAgICAgICBheGlvcyh7DQogICAgICAgICAgICAgIG1ldGhvZDogInBvc3QiLA0KICAgICAgICAgICAgICBoZWFkZXJzOiB7DQogICAgICAgICAgICAgICAgQXV0aG9yaXphdGlvbjogIkJlYXJlciAiICsgZ2V0VG9rZW4oKSwNCiAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgdXJsOg0KICAgICAgICAgICAgICAgIHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKw0KICAgICAgICAgICAgICAgIHVybE1hcFt0aGlzLmV4cG9ydEZvcm0uZXhwb3J0VHlwZV0sDQogICAgICAgICAgICAgIGRhdGE6IHBhcmFtcywNCiAgICAgICAgICAgICAgcmVzcG9uc2VUeXBlOiAiYmxvYiIsDQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICAgICAgdGhpcy4kbW9kYWwuY2xvc2VMb2FkaW5nKCk7DQogICAgICAgICAgICAgICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoImEiKTsNCiAgICAgICAgICAgICAgICBsZXQgYmxvYiA9IG5ldyBCbG9iKFtyZXMuZGF0YV0sIHsNCiAgICAgICAgICAgICAgICAgIHR5cGU6ICJhcHBsaWNhdGlvbi92bmQubXMtZXhjZWwiLA0KICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICAgIGxpbmsuc3R5bGUuZGlzcGxheSA9ICJub25lIjsNCiAgICAgICAgICAgICAgICBsaW5rLmhyZWYgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpOw0KICAgICAgICAgICAgICAgIGxpbmsuZG93bmxvYWQgPQ0KICAgICAgICAgICAgICAgICAgZmlsZU5hbWVNYXBbdGhpcy5leHBvcnRGb3JtLmV4cG9ydFR5cGVdICsgIi54bHN4IjsNCiAgICAgICAgICAgICAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKGxpbmspOw0KICAgICAgICAgICAgICAgIGxpbmsuY2xpY2soKTsNCiAgICAgICAgICAgICAgICBkb2N1bWVudC5ib2R5LnJlbW92ZUNoaWxkKGxpbmspOw0KICAgICAgICAgICAgICAgIHRoaXMuZXhwb3J0RGlhbG9nID0gZmFsc2U7DQogICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgIC5jYXRjaCgoKSA9PiB7DQogICAgICAgICAgICAgICAgdGhpcy4kbW9kYWwuY2xvc2VMb2FkaW5nKCk7DQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDlr7zlh7rmoLjplIDmmI7nu4YgKi8NCiAgICBoYW5kbGVFeHBvcnRXcml0ZU9mZkRldGFpbCgpIHsNCiAgICAgIHRoaXMuJG1vZGFsLmxvYWRpbmcoIuato+WcqOWvvOWHuuaguOmUgOaYjue7hu+8jOivt+eojeWAmSIpOw0KDQogICAgICBheGlvcyh7DQogICAgICAgIG1ldGhvZDogInBvc3QiLA0KICAgICAgICBoZWFkZXJzOiB7DQogICAgICAgICAgQXV0aG9yaXphdGlvbjogIkJlYXJlciAiICsgZ2V0VG9rZW4oKSwNCiAgICAgICAgfSwNCiAgICAgICAgdXJsOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgIi9idXNpbmVzcy93b3JrRGF0YS9leHBvcnRDYXJkIiwNCiAgICAgICAgcmVzcG9uc2VUeXBlOiAiYmxvYiIsDQogICAgICB9KQ0KICAgICAgICAudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgdGhpcy4kbW9kYWwuY2xvc2VMb2FkaW5nKCk7DQogICAgICAgICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoImEiKTsNCiAgICAgICAgICBsZXQgYmxvYiA9IG5ldyBCbG9iKFtyZXMuZGF0YV0sIHsNCiAgICAgICAgICAgIHR5cGU6ICJhcHBsaWNhdGlvbi92bmQubXMtZXhjZWwiLA0KICAgICAgICAgIH0pOw0KICAgICAgICAgIGxpbmsuc3R5bGUuZGlzcGxheSA9ICJub25lIjsNCiAgICAgICAgICBsaW5rLmhyZWYgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpOw0KICAgICAgICAgIGxpbmsuZG93bmxvYWQgPSAi5qC46ZSA5piO57uGLnhsc3giOw0KICAgICAgICAgIGRvY3VtZW50LmJvZHkuYXBwZW5kQ2hpbGQobGluayk7DQogICAgICAgICAgbGluay5jbGljaygpOw0KICAgICAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQobGluayk7DQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5a+85Ye65oiQ5YqfIik7DQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoKSA9PiB7DQogICAgICAgICAgdGhpcy4kbW9kYWwuY2xvc2VMb2FkaW5nKCk7DQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuWvvOWHuuWksei0pe+8jOivt+mHjeivlSIpOw0KICAgICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDliKTmlq3ooYzmmK/lkKblj6/pgIkgKi8NCiAgICBjaGVja1NlbGVjdGFibGUocm93KSB7DQogICAgICByZXR1cm4gIXJvdy5yZWRDYXJkOyAvLyByZWRDYXJk5Li6MeaXtui/lOWbnmZhbHNl77yM6KGo56S65LiN5Y+v6YCJDQogICAgfSwNCiAgICAvKiog5pi+56S65Y+R6YCB5Y2h5Yi45YiX6KGo5by55qGGICovDQogICAgaGFuZGxlU2VuZENhcmRMaXN0RGlhbG9nKCkgew0KICAgICAgdGhpcy5zZW5kQ2FyZExpc3REaWFsb2cgPSB0cnVlOw0KICAgICAgdGhpcy5zZW5kQ2FyZExpc3RGb3JtID0gew0KICAgICAgICBzdG9ja0lkOiAiIiwNCiAgICAgIH07DQogICAgfSwNCiAgICAvKiog56Gu6K6k5Y+R6YCB5Y2h5Yi45YiX6KGo5pON5L2cICovDQogICAgY29uZmlybVNlbmRDYXJkTGlzdCgpIHsNCiAgICAgIHRoaXMuJHJlZnMuc2VuZENhcmRMaXN0Rm9ybS52YWxpZGF0ZShhc3luYyAodmFsaWQpID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgIHRoaXMuJG1vZGFsLmxvYWRpbmcoIuato+WcqOWPkemAgeWNoeWIuOWIl+ihqO+8jOivt+eojeWAmS4uLiIpOw0KICAgICAgICAgICAgYXdhaXQgc2VuZENhcmRMaXN0KHRoaXMuc2VuZENhcmRMaXN0Rm9ybS5zdG9ja0lkKTsNCiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWPkemAgeWNoeWIuOWIl+ihqOaIkOWKnyIpOw0KICAgICAgICAgICAgdGhpcy5zZW5kQ2FyZExpc3REaWFsb2cgPSBmYWxzZTsNCiAgICAgICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICAgICAgY29uc29sZS5lcnJvcigi5Y+R6YCB5Y2h5Yi45YiX6KGo5aSx6LSlOiIsIGVycm9yKTsNCiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLlj5HpgIHljaHliLjliJfooajlpLHotKXvvIzor7fph43or5UiKTsNCiAgICAgICAgICB9IGZpbmFsbHkgew0KICAgICAgICAgICAgdGhpcy4kbW9kYWwuY2xvc2VMb2FkaW5nKCk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICB9LA0KICB3YXRjaDogew0KICAgIC8vIOebkeWQrOaVsOaNruWPmOWMlu+8jOa4heepuumAieS4reeKtuaAgQ0KICAgIHRhYmxlRGF0YSgpIHsNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgaWYgKHRoaXMuJHJlZnMudGFibGUpIHsNCiAgICAgICAgICB0aGlzLiRyZWZzLnRhYmxlLmNsZWFyU2VsZWN0aW9uKCk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgSA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/mThree/curls", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"10\" class=\"mb5\">\r\n      <!-- 右对齐搜索区域 -->\r\n      <el-col>\r\n        <el-form\r\n          :model=\"queryParams\"\r\n          ref=\"queryForm\"\r\n          :inline=\"true\"\r\n          label-width=\"80px\"\r\n        >\r\n          <el-form-item\r\n            label=\"部门\"\r\n            prop=\"deptName\"\r\n            class=\"el-form-search-item\"\r\n          >\r\n            <el-input\r\n              v-model=\"queryParams.deptName\"\r\n              placeholder=\"请输入部门名称\"\r\n              clearable\r\n              size=\"small\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item\r\n            label=\"业务员\"\r\n            prop=\"applyName\"\r\n            class=\"el-form-search-item\"\r\n          >\r\n            <el-input\r\n              v-model=\"queryParams.applyName\"\r\n              placeholder=\"请输入员工姓名\"\r\n              clearable\r\n              size=\"small\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item\r\n            label=\"发放状态\"\r\n            prop=\"redCard\"\r\n            class=\"el-form-search-item\"\r\n          >\r\n            <el-select\r\n              v-model=\"queryParams.redCard\"\r\n              clearable\r\n              placeholder=\"请选择发放状态\"\r\n            >\r\n              <el-option label=\"待发放\" :value=\"0\" />\r\n              <el-option label=\"已发放\" :value=\"1\" />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item\r\n            label=\"时间范围\"\r\n            prop=\"dateRange\"\r\n            class=\"el-form-search-item\"\r\n          >\r\n            <el-date-picker\r\n              v-model=\"dateRange\"\r\n              type=\"daterange\"\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n              value-format=\"yyyy-MM-dd\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item class=\"el-form-search-item\">\r\n            <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-search\"\r\n              size=\"mini\"\r\n              @click=\"handleQuery\"\r\n              >搜索</el-button\r\n            >\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n              >重置</el-button\r\n            >\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 操作按钮区域 -->\r\n    <el-row class=\"mb8\" :gutter=\"10\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          size=\"mini\"\r\n          @click=\"handleResend\"\r\n          :disabled=\"!selectedRows.length\"\r\n        >\r\n          一键补发卡卷\r\n        </el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExportDialog\"\r\n        >\r\n          导出数据\r\n        </el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"info\"\r\n          plain\r\n          icon=\"el-icon-document-copy\"\r\n          size=\"mini\"\r\n          @click=\"handleExportWriteOffDetail\"\r\n        >\r\n          导出核销明细\r\n        </el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-s-promotion\"\r\n          size=\"mini\"\r\n          @click=\"handleSendCardListDialog\"\r\n        >\r\n          发送一元卡券\r\n        </el-button>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"tableData\"\r\n      border\r\n      style=\"width: 100%\"\r\n      :height=\"tableHeight\"\r\n      @selection-change=\"handleSelectionChange\"\r\n      ref=\"table\"\r\n    >\r\n      <el-table-column\r\n        type=\"selection\"\r\n        width=\"55\"\r\n        align=\"center\"\r\n        :selectable=\"checkSelectable\"\r\n      />\r\n      <el-table-column label=\"#\" type=\"index\" width=\"50\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{\r\n            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1\r\n          }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"nickName\" label=\"客户姓名\" align=\"center\" />\r\n      <el-table-column\r\n        prop=\"phonenumber\"\r\n        label=\"客户手机号\"\r\n        align=\"center\"\r\n        width=\"120\"\r\n      />\r\n      <!-- <el-table-column prop=\"orderId\" label=\"订单ID\" align=\"center\" /> -->\r\n\r\n      <el-table-column prop=\"applyName\" label=\"业务员\" align=\"center\" />\r\n      <el-table-column prop=\"deptName\" label=\"所属部门\" align=\"center\" />\r\n      <el-table-column prop=\"redCard\" label=\"发放状态\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag :type=\"scope.row.redCard ? 'success' : 'primary'\">\r\n            {{ scope.row.redCard ? \"已发放\" : \"待发放\" }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"orderAmount\" label=\"红包金额\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          {{\r\n            scope.row.orderAmount ? scope.row.orderAmount + \"元\" : \"谢谢惠顾\"\r\n          }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        prop=\"createTime\"\r\n        label=\"发放时间\"\r\n        align=\"center\"\r\n        width=\"160\"\r\n      />\r\n      <el-table-column label=\"操作\" align=\"center\" width=\"200\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            v-if=\"!scope.row.redCard\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-s-promotion\"\r\n            @click=\"handleEdit(scope.row)\"\r\n            >补发卡卷</el-button\r\n          >\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      :page-sizes=\"[20, 30, 50, 100]\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加导出选项弹框 -->\r\n    <el-dialog\r\n      title=\"导出选项\"\r\n      :visible.sync=\"exportDialog\"\r\n      width=\"500px\"\r\n      append-to-body\r\n    >\r\n      <el-form\r\n        ref=\"exportForm\"\r\n        :model=\"exportForm\"\r\n        :rules=\"exportRules\"\r\n        label-width=\"80px\"\r\n      >\r\n        <el-form-item label=\"时间范围\" prop=\"dateRange\">\r\n          <el-date-picker\r\n            v-model=\"exportForm.dateRange\"\r\n            type=\"daterange\"\r\n            range-separator=\"至\"\r\n            start-placeholder=\"开始日期\"\r\n            end-placeholder=\"结束日期\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            style=\"width: 100%\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"导出类型\" prop=\"exportType\">\r\n          <el-select\r\n            v-model=\"exportForm.exportType\"\r\n            placeholder=\"请选择导出类型\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option label=\"客户领取明细\" value=\"customer\" />\r\n            <el-option label=\"员工发卷明细\" value=\"staff\" />\r\n            <el-option label=\"项目组发卷明细\" value=\"dept\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"exportDialog = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmExport\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 发送卡券列表弹框 -->\r\n    <el-dialog\r\n      title=\"发送卡券列表\"\r\n      :visible.sync=\"sendCardListDialog\"\r\n      width=\"400px\"\r\n      append-to-body\r\n    >\r\n      <el-form\r\n        ref=\"sendCardListForm\"\r\n        :model=\"sendCardListForm\"\r\n        :rules=\"sendCardListRules\"\r\n        label-width=\"80px\"\r\n      >\r\n        <el-form-item label=\"批次码\" prop=\"stockId\">\r\n          <el-input\r\n            v-model=\"sendCardListForm.stockId\"\r\n            placeholder=\"请输入批次码\"\r\n            clearable\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"批次选项\" prop=\"type\">\r\n          <el-select\r\n            v-model=\"sendCardListForm.type\"\r\n            placeholder=\"请选择批次\"\r\n            clearable\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option label=\"批次1\" :value=\"1\" />\r\n            <el-option label=\"批次2\" :value=\"2\" />\r\n            <el-option label=\"批次3\" :value=\"3\" />\r\n            <el-option label=\"批次4\" :value=\"4\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"sendCardListDialog = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmSendCardList\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from \"axios\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport { getCurlsList, sendCard, sendCardList } from \"@/api/system/curls\";\r\n\r\nexport default {\r\n  name: \"Curls\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 总条数\r\n      total: 0,\r\n      // 表格数据\r\n      tableData: [],\r\n      // 查询参数\r\n      dateRange: [],\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        deptName: undefined,\r\n        applyName: undefined,\r\n        redCard: undefined,\r\n        startTime: undefined,\r\n        endTime: undefined,\r\n      },\r\n      // 导出弹框显示状态\r\n      exportDialog: false,\r\n      // 导出表单参数\r\n      exportForm: {\r\n        dateRange: [],\r\n        exportType: undefined,\r\n      },\r\n      // 导出表单校验规则\r\n      exportRules: {\r\n        dateRange: [\r\n          { required: true, message: \"请选择时间范围\", trigger: \"change\" },\r\n        ],\r\n        exportType: [\r\n          { required: true, message: \"请选择导出类型\", trigger: \"change\" },\r\n        ],\r\n      },\r\n      tableHeight: 650,\r\n      selectedRows: [], // 新增选中行数组\r\n      // 发送卡券列表弹框显示状态\r\n      sendCardListDialog: false,\r\n      // 发送卡券列表表单参数\r\n      sendCardListForm: {\r\n        stockId: \"\",\r\n      },\r\n      // 发送卡券列表表单校验规则\r\n      sendCardListRules: {\r\n        stockId: [{ required: true, message: \"请输入批次码\", trigger: \"blur\" }],\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询列表 */\r\n    async getList() {\r\n      this.loading = true;\r\n\r\n      // 处理时间参数\r\n      if (this.dateRange && this.dateRange.length === 2) {\r\n        this.queryParams.startTime = this.dateRange[0];\r\n        this.queryParams.endTime = this.dateRange[1];\r\n      }\r\n\r\n      try {\r\n        const res = await getCurlsList(this.queryParams);\r\n        this.tableData = res.rows;\r\n        this.total = res.total;\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.dateRange = [];\r\n      this.selectedRows = [];\r\n      if (this.$refs.table) {\r\n        this.$refs.table.clearSelection();\r\n      }\r\n      this.handleQuery();\r\n    },\r\n    /** 补发按钮操作 */\r\n    handleEdit(row) {\r\n      this.$modal\r\n        .confirm(`确认要对\"${row.nickName}\"进行补发操作吗？`)\r\n        .then(async () => {\r\n          try {\r\n            this.$modal.loading(\"正在补发中，请稍候...\");\r\n            // 调用补发接口，单个补发时传入单个id的数组\r\n            await sendCard({ list: [row.openid] });\r\n            this.$modal.msgSuccess(\"补发成功\");\r\n            this.getList();\r\n          } catch (error) {\r\n            console.error(\"补发失败:\", error);\r\n            this.$modal.msgError(\"补发失败，请重试\");\r\n          } finally {\r\n            this.$modal.closeLoading();\r\n          }\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /** 表格多选框选中数据 */\r\n    handleSelectionChange(selection) {\r\n      this.selectedRows = selection.filter((row) => !row.redCard);\r\n    },\r\n    /** 一键补发按钮操作 */\r\n    handleResend() {\r\n      if (!this.selectedRows.length) {\r\n        this.$modal.msgError(\"请先选择要补发的客户\");\r\n        return;\r\n      }\r\n\r\n      const names = this.selectedRows.map((item) => item.nickName).join(\"、\");\r\n      this.$modal\r\n        .confirm(`确认要对\"${names}\"进行补发操作吗？`)\r\n        .then(async () => {\r\n          try {\r\n            this.$modal.loading(\"正在补发中，请稍候...\");\r\n            // 调用补发接口，传入选中行的openid数组\r\n            const openids = this.selectedRows.map((item) => item.openid);\r\n            await sendCard({ list: openids });\r\n            this.$modal.msgSuccess(\"补发成功\");\r\n            this.getList();\r\n          } catch (error) {\r\n            console.error(\"补发失败:\", error);\r\n            this.$modal.msgError(\"补发失败，请重试\");\r\n          } finally {\r\n            this.$modal.closeLoading();\r\n          }\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /** 显示导出弹框 */\r\n    handleExportDialog() {\r\n      this.exportDialog = true;\r\n      this.exportForm = {\r\n        dateRange: [],\r\n        exportType: undefined,\r\n      };\r\n    },\r\n    /** 确认导出操作 */\r\n    confirmExport() {\r\n      this.$refs.exportForm.validate((valid) => {\r\n        if (valid) {\r\n          const params = {\r\n            startTime: this.exportForm.dateRange[0],\r\n            endTime: this.exportForm.dateRange[1],\r\n          };\r\n\r\n          this.$modal.confirm(\"是否确认导出数据？\").then(() => {\r\n            this.$modal.loading(\"正在导出数据，请稍候\");\r\n\r\n            // 根据选择的导出类型确定接口地址\r\n            const urlMap = {\r\n              customer: \"/system/curls/export\",\r\n              staff: \"/system/curls/exportStaff\",\r\n              dept: \"/system/curls/exportDept\",\r\n            };\r\n\r\n            // 根据选择的导出类型确定文件名\r\n            const fileNameMap = {\r\n              customer: `${params.startTime}-${params.endTime}客户领取明细`,\r\n              staff: `${params.startTime}-${params.endTime}员工发卷明细`,\r\n              dept: `${params.startTime}-${params.endTime}项目组发卷明细`,\r\n            };\r\n\r\n            axios({\r\n              method: \"post\",\r\n              headers: {\r\n                Authorization: \"Bearer \" + getToken(),\r\n              },\r\n              url:\r\n                process.env.VUE_APP_BASE_API +\r\n                urlMap[this.exportForm.exportType],\r\n              data: params,\r\n              responseType: \"blob\",\r\n            })\r\n              .then((res) => {\r\n                this.$modal.closeLoading();\r\n                const link = document.createElement(\"a\");\r\n                let blob = new Blob([res.data], {\r\n                  type: \"application/vnd.ms-excel\",\r\n                });\r\n                link.style.display = \"none\";\r\n                link.href = URL.createObjectURL(blob);\r\n                link.download =\r\n                  fileNameMap[this.exportForm.exportType] + \".xlsx\";\r\n                document.body.appendChild(link);\r\n                link.click();\r\n                document.body.removeChild(link);\r\n                this.exportDialog = false;\r\n              })\r\n              .catch(() => {\r\n                this.$modal.closeLoading();\r\n              });\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 导出核销明细 */\r\n    handleExportWriteOffDetail() {\r\n      this.$modal.loading(\"正在导出核销明细，请稍候\");\r\n\r\n      axios({\r\n        method: \"post\",\r\n        headers: {\r\n          Authorization: \"Bearer \" + getToken(),\r\n        },\r\n        url: process.env.VUE_APP_BASE_API + \"/business/workData/exportCard\",\r\n        responseType: \"blob\",\r\n      })\r\n        .then((res) => {\r\n          this.$modal.closeLoading();\r\n          const link = document.createElement(\"a\");\r\n          let blob = new Blob([res.data], {\r\n            type: \"application/vnd.ms-excel\",\r\n          });\r\n          link.style.display = \"none\";\r\n          link.href = URL.createObjectURL(blob);\r\n          link.download = \"核销明细.xlsx\";\r\n          document.body.appendChild(link);\r\n          link.click();\r\n          document.body.removeChild(link);\r\n          this.$modal.msgSuccess(\"导出成功\");\r\n        })\r\n        .catch(() => {\r\n          this.$modal.closeLoading();\r\n          this.$modal.msgError(\"导出失败，请重试\");\r\n        });\r\n    },\r\n    /** 判断行是否可选 */\r\n    checkSelectable(row) {\r\n      return !row.redCard; // redCard为1时返回false，表示不可选\r\n    },\r\n    /** 显示发送卡券列表弹框 */\r\n    handleSendCardListDialog() {\r\n      this.sendCardListDialog = true;\r\n      this.sendCardListForm = {\r\n        stockId: \"\",\r\n      };\r\n    },\r\n    /** 确认发送卡券列表操作 */\r\n    confirmSendCardList() {\r\n      this.$refs.sendCardListForm.validate(async (valid) => {\r\n        if (valid) {\r\n          try {\r\n            this.$modal.loading(\"正在发送卡券列表，请稍候...\");\r\n            await sendCardList(this.sendCardListForm.stockId);\r\n            this.$modal.msgSuccess(\"发送卡券列表成功\");\r\n            this.sendCardListDialog = false;\r\n          } catch (error) {\r\n            console.error(\"发送卡券列表失败:\", error);\r\n            this.$modal.msgError(\"发送卡券列表失败，请重试\");\r\n          } finally {\r\n            this.$modal.closeLoading();\r\n          }\r\n        }\r\n      });\r\n    },\r\n  },\r\n  watch: {\r\n    // 监听数据变化，清空选中状态\r\n    tableData() {\r\n      this.$nextTick(() => {\r\n        if (this.$refs.table) {\r\n          this.$refs.table.clearSelection();\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n.mb5 {\r\n  margin-bottom: 5px;\r\n}\r\n.mb8 {\r\n  margin-top: 20px;\r\n  margin-bottom: 8px;\r\n}\r\n.el-form-search-item {\r\n  margin-bottom: 0;\r\n}\r\n</style>\r\n"]}]}