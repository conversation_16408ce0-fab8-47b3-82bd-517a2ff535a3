import request from "@/utils/request";

// 获取打款列表
export function getMoneyList(query) {
  return request({
    url: "/system/money/list",
    method: "get",
    params: query,
  });
}

// 新增打款记录
export function addMoney(data) {
  return request({
    url: "/system/money",
    method: "post",
    data: data,
  });
}

// 修改打款记录
export function updateMoney(data) {
  return request({
    url: "/system/money",
    method: "put",
    data: data,
  });
}

// 删除打款记录
export function deleteMoney(id) {
  return request({
    url: `/system/money/${id}`,
    method: "delete",
  });
}
