
e554e158cb17d805071c25643ea97f6b6f969a91	{"key":"{\"nodeVersion\":\"v16.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"f23e7bc6334a7984452808eeffe50e1b\"}","integrity":"sha512-46E7PbwMYaO8LzLuEvF4hymTCntlSJsTceQ4uSMqjevsurqjiMiWligmvhVc47C654v3MabxRE6GQ+rQ9oyz8Q==","time":1753843716960,"size":11432754}