<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
export default {
  name: "App",
  metaInfo() {
    return {
      title:
        this.$store.state.settings.dynamicTitle &&
        this.$store.state.settings.title,
      titleTemplate: (title) => {
        return title
          ? `${title} - ${process.env.VUE_APP_TITLE}`
          : process.env.VUE_APP_TITLE;
      },
    };
  },
};
</script>
<style>
.flex {
  display: flex;
}
.jcc {
  justify-content: center;
}
.jca {
  justify-content: space-around;
}
.jce {
  justify-content: space-evenly;
}
.jcb {
  justify-content: space-between;
}
.aic {
  align-items: center;
}
.mt10 {
  margin-top: 10px;
}
.mb10 {
  margin-bottom: 10px;
}
</style>
