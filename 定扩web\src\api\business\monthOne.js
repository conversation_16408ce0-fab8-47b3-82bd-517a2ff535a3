import request from "@/utils/request";

// 查询M0核销统计列表
export function getMonthThirdList(query) {
  return request({
    url: "/business/monthOne/list",
    method: "get",
    params: query,
  });
}

// 导出M0核销统计模板
export function exportBankRecordModel() {
  return request({
    url: "/business/monthOne/exportModel",
    method: "post",
    responseType: "blob",
  });
}

// 导入M0核销统计数据
export function importMonthThirdData(data) {
  return request({
    url: "/business/monthOne/upload",
    method: "post",
    data: data,
  });
}
