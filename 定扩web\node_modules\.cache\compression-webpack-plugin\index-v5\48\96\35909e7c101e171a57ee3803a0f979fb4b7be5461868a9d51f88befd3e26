
17e5ffec0877cc9618f83352ce98d04462d79d45	{"key":"{\"nodeVersion\":\"v16.20.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"584bd63cc3f518a960d9ea203775f5e3\"}","integrity":"sha512-4h88kQsvi1VOHLr1A8LOVFLBxWe7rCOfhwM8zfp942xboU4YoAxYigYOpJ4U1v/fJhX4lPq9PqEIiedUZFvSBA==","time":1753668217798,"size":3428828}